#!/usr/bin/env python3
"""
API Simple pour WebSec Scanner
FastAPI sans classes, code direct
"""

import os
import json
import sqlite3
import subprocess
import threading
from datetime import datetime
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse
from pydantic import BaseModel
import uvicorn

# Configuration
DATABASE_FILE = "websec_results.db"
RESULTS_DIR = "results"

# Créer l'app FastAPI
app = FastAPI(
    title="WebSec Scanner API",
    description="API simple pour scanner de sécurité web",
    version="1.0.0"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models Pydantic simples
class ScanRequest(BaseModel):
    target: str
    tools: list = ["nmap", "nuclei", "nikto", "ssl"]

class ScanResponse(BaseModel):
    scan_id: str
    target: str
    status: str
    message: str

# Initialiser la base de données
def init_db():
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS scans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        target TEXT NOT NULL,
        status TEXT NOT NULL,
        tools TEXT NOT NULL,
        started_at TEXT NOT NULL,
        completed_at TEXT,
        report_file TEXT
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS scan_results (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        scan_id INTEGER,
        target TEXT NOT NULL,
        tool TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        vulnerabilities TEXT,
        severity_counts TEXT,
        raw_output TEXT,
        execution_time REAL,
        FOREIGN KEY (scan_id) REFERENCES scans (id)
    )
    ''')
    
    conn.commit()
    conn.close()

# Exécuter le scanner en arrière-plan
def run_scanner_background(scan_id, target, tools):
    # Mettre à jour le statut
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    cursor.execute("UPDATE scans SET status = 'running' WHERE id = ?", (scan_id,))
    conn.commit()
    conn.close()
    
    try:
        # Construire la commande
        tools_str = ','.join(tools)
        command = f"python websec_scanner.py {target} {tools_str}"
        
        # Exécuter le scanner
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=1800)
        
        if result.returncode == 0:
            status = "completed"
        else:
            status = "failed"
        
        # Mettre à jour le statut final
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE scans 
            SET status = ?, completed_at = ? 
            WHERE id = ?
        """, (status, datetime.now().isoformat(), scan_id))
        conn.commit()
        conn.close()
        
    except Exception as e:
        # Marquer comme échoué
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE scans 
            SET status = 'failed', completed_at = ? 
            WHERE id = ?
        """, (datetime.now().isoformat(), scan_id))
        conn.commit()
        conn.close()

# Routes API
@app.get("/")
def root():
    return {
        "message": "WebSec Scanner API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": [
            "GET /scans - Lister les scans",
            "POST /scans - Créer un nouveau scan",
            "GET /scans/{scan_id} - Détails d'un scan",
            "GET /scans/{scan_id}/results - Résultats d'un scan",
            "GET /scans/{scan_id}/report - Télécharger le rapport"
        ]
    }

@app.get("/health")
def health_check():
    try:
        # Vérifier la base de données
        conn = sqlite3.connect(DATABASE_FILE)
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        conn.close()
        
        return {
            "status": "healthy",
            "database": "connected",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unavailable: {e}")

@app.post("/scans", response_model=ScanResponse)
def create_scan(scan_request: ScanRequest, background_tasks: BackgroundTasks):
    # Valider le target
    if not scan_request.target.strip():
        raise HTTPException(status_code=400, detail="Target ne peut pas être vide")
    
    # Créer l'entrée de scan
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    
    cursor.execute("""
        INSERT INTO scans (target, status, tools, started_at)
        VALUES (?, ?, ?, ?)
    """, (
        scan_request.target,
        "pending",
        json.dumps(scan_request.tools),
        datetime.now().isoformat()
    ))
    
    scan_id = cursor.lastrowid
    conn.commit()
    conn.close()
    
    # Lancer le scan en arrière-plan
    background_tasks.add_task(run_scanner_background, scan_id, scan_request.target, scan_request.tools)
    
    return ScanResponse(
        scan_id=str(scan_id),
        target=scan_request.target,
        status="pending",
        message="Scan démarré avec succès"
    )

@app.get("/scans")
def list_scans(limit: int = 20, offset: int = 0):
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT id, target, status, tools, started_at, completed_at
        FROM scans 
        ORDER BY started_at DESC 
        LIMIT ? OFFSET ?
    """, (limit, offset))
    
    scans = []
    for row in cursor.fetchall():
        scan_id, target, status, tools, started_at, completed_at = row
        scans.append({
            "scan_id": scan_id,
            "target": target,
            "status": status,
            "tools": json.loads(tools),
            "started_at": started_at,
            "completed_at": completed_at
        })
    
    conn.close()
    return {"scans": scans, "total": len(scans)}

@app.get("/scans/{scan_id}")
def get_scan(scan_id: int):
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    
    cursor.execute("SELECT * FROM scans WHERE id = ?", (scan_id,))
    scan = cursor.fetchone()
    
    if not scan:
        raise HTTPException(status_code=404, detail="Scan non trouvé")
    
    # Compter les vulnérabilités
    cursor.execute("""
        SELECT 
            COUNT(*) as total_vulns,
            SUM(CASE WHEN json_extract(severity_counts, '$.critical') > 0 THEN json_extract(severity_counts, '$.critical') ELSE 0 END) as critical,
            SUM(CASE WHEN json_extract(severity_counts, '$.high') > 0 THEN json_extract(severity_counts, '$.high') ELSE 0 END) as high,
            SUM(CASE WHEN json_extract(severity_counts, '$.medium') > 0 THEN json_extract(severity_counts, '$.medium') ELSE 0 END) as medium
        FROM scan_results 
        WHERE scan_id = ?
    """, (scan_id,))
    
    vuln_stats = cursor.fetchone()
    conn.close()
    
    scan_id, target, status, tools, started_at, completed_at, report_file = scan
    
    return {
        "scan_id": scan_id,
        "target": target,
        "status": status,
        "tools": json.loads(tools),
        "started_at": started_at,
        "completed_at": completed_at,
        "report_file": report_file,
        "vulnerability_stats": {
            "total": vuln_stats[0] or 0,
            "critical": vuln_stats[1] or 0,
            "high": vuln_stats[2] or 0,
            "medium": vuln_stats[3] or 0
        }
    }

@app.get("/scans/{scan_id}/results")
def get_scan_results(scan_id: int):
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    
    # Vérifier que le scan existe
    cursor.execute("SELECT id FROM scans WHERE id = ?", (scan_id,))
    if not cursor.fetchone():
        raise HTTPException(status_code=404, detail="Scan non trouvé")
    
    # Récupérer les résultats
    cursor.execute("""
        SELECT tool, vulnerabilities, severity_counts, execution_time, timestamp
        FROM scan_results 
        WHERE scan_id = ?
        ORDER BY timestamp
    """, (scan_id,))
    
    results = []
    for row in cursor.fetchall():
        tool, vulnerabilities, severity_counts, execution_time, timestamp = row
        results.append({
            "tool": tool,
            "vulnerabilities": json.loads(vulnerabilities) if vulnerabilities else [],
            "severity_counts": json.loads(severity_counts) if severity_counts else {},
            "execution_time": execution_time,
            "timestamp": timestamp
        })
    
    conn.close()
    return {"scan_id": scan_id, "results": results}

@app.get("/scans/{scan_id}/report")
def download_report(scan_id: int):
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    
    cursor.execute("SELECT target, report_file FROM scans WHERE id = ?", (scan_id,))
    scan = cursor.fetchone()
    conn.close()
    
    if not scan:
        raise HTTPException(status_code=404, detail="Scan non trouvé")
    
    target, report_file = scan
    
    # Chercher le fichier de rapport
    if not report_file:
        # Chercher dans le répertoire results
        import glob
        pattern = f"{RESULTS_DIR}/rapport_{target.replace('/', '_').replace(':', '_')}*.html"
        files = glob.glob(pattern)
        if files:
            report_file = files[-1]  # Le plus récent
    
    if not report_file or not os.path.exists(report_file):
        raise HTTPException(status_code=404, detail="Rapport non trouvé")
    
    return FileResponse(
        path=report_file,
        filename=f"rapport_securite_{scan_id}.html",
        media_type="text/html"
    )

@app.get("/dashboard", response_class=HTMLResponse)
def dashboard():
    # Dashboard simple en HTML
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>WebSec Scanner Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
            .form-section { background: #ecf0f1; padding: 20px; margin: 20px 0; border-radius: 5px; }
            .results { margin: 20px 0; }
            input, select, button { padding: 10px; margin: 5px; }
            button { background: #3498db; color: white; border: none; border-radius: 3px; cursor: pointer; }
            button:hover { background: #2980b9; }
            .scan-item { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #3498db; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🛡️ WebSec Scanner Dashboard</h1>
            <p>Interface simple pour lancer des scans de sécurité</p>
        </div>
        
        <div class="form-section">
            <h3>🚀 Nouveau Scan</h3>
            <form id="scanForm">
                <input type="text" id="target" placeholder="example.com ou https://example.com" required style="width: 300px;">
                <select id="tools" multiple style="width: 200px; height: 100px;">
                    <option value="nmap" selected>Nmap</option>
                    <option value="nuclei" selected>Nuclei</option>
                    <option value="nikto" selected>Nikto</option>
                    <option value="ssl" selected>SSL Test</option>
                </select>
                <button type="submit">Lancer le Scan</button>
            </form>
        </div>
        
        <div class="results">
            <h3>📊 Scans Récents</h3>
            <div id="scansList">Chargement...</div>
        </div>
        
        <script>
            // Charger les scans
            async function loadScans() {
                try {
                    const response = await fetch('/scans');
                    const data = await response.json();
                    const scansList = document.getElementById('scansList');
                    
                    if (data.scans.length === 0) {
                        scansList.innerHTML = '<p>Aucun scan trouvé.</p>';
                        return;
                    }
                    
                    scansList.innerHTML = data.scans.map(scan => `
                        <div class="scan-item">
                            <strong>ID: ${scan.scan_id}</strong> - ${scan.target} 
                            <span style="color: ${scan.status === 'completed' ? 'green' : scan.status === 'failed' ? 'red' : 'orange'}">
                                [${scan.status.toUpperCase()}]
                            </span>
                            <br>
                            <small>Démarré: ${new Date(scan.started_at).toLocaleString()}</small>
                            ${scan.status === 'completed' ? `<br><a href="/scans/${scan.scan_id}/report" target="_blank">📄 Voir le rapport</a>` : ''}
                        </div>
                    `).join('');
                } catch (error) {
                    document.getElementById('scansList').innerHTML = '<p>Erreur lors du chargement des scans.</p>';
                }
            }
            
            // Soumettre un nouveau scan
            document.getElementById('scanForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const target = document.getElementById('target').value;
                const toolsSelect = document.getElementById('tools');
                const tools = Array.from(toolsSelect.selectedOptions).map(option => option.value);
                
                try {
                    const response = await fetch('/scans', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ target, tools })
                    });
                    
                    const result = await response.json();
                    alert(`Scan démarré avec succès! ID: ${result.scan_id}`);
                    loadScans(); // Recharger la liste
                } catch (error) {
                    alert('Erreur lors du lancement du scan');
                }
            });
            
            // Charger les scans au démarrage
            loadScans();
            
            // Recharger toutes les 10 secondes
            setInterval(loadScans, 10000);
        </script>
    </body>
    </html>
    """
    return html_content

# Initialiser la base de données au démarrage
init_db()

# Démarrer le serveur
uvicorn.run(app, host="0.0.0.0", port=8000)
