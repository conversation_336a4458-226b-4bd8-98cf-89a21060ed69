# 🛡️ WebSec Scanner Simple

Scanner de sécurité web **sans Docker, sans classes**, avec du code direct et efficace.

## 🚀 Installation Rapide

### Ubuntu/Debian (Recommandé)
```bash
# Télécharger et installer
sudo chmod +x install.sh
sudo ./install.sh

# Ou installation manuelle:
sudo apt update
sudo apt install -y python3 python3-pip nmap nikto golang-go nodejs npm
pip3 install -r requirements.txt

# Installer Nuclei
go install github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
sudo cp ~/go/bin/nuclei /usr/local/bin/

# Installer testssl.sh
git clone https://github.com/drwetter/testssl.sh.git
sudo cp testssl.sh/testssl.sh /usr/local/bin/
```

### Windows (avec WSL)
```bash
# Installer WSL Ubuntu puis suivre les instructions Ubuntu
wsl --install -d Ubuntu
```

## 📁 Structure Simple

```
websec_scanner.py     # Scanner principal (sans classes)
websec_api.py         # API simple (sans classes)
n8n_workflow_simple.json  # Workflow n8n
requirements.txt      # Dépendances Python
install.sh           # Script d'installation
results/             # Résultats des scans
websec_results.db    # Base SQLite
```

## 🔍 Utilisation

### 1. Scanner en ligne de commande

```bash
# Scan complet
python3 websec_scanner.py example.com

# Scan avec outils spécifiques
python3 websec_scanner.py example.com nmap,nuclei

# Scan SSL uniquement
python3 websec_scanner.py https://example.com ssl

# Lister les scans précédents
python3 websec_scanner.py --list
```

### 2. API Web

```bash
# Démarrer l'API
python3 websec_api.py

# Accéder au dashboard
http://localhost:8000/dashboard

# Documentation API
http://localhost:8000/docs
```

### 3. Via API REST

```bash
# Lancer un scan
curl -X POST "http://localhost:8000/scans" \
  -H "Content-Type: application/json" \
  -d '{"target": "example.com", "tools": ["nmap", "nuclei"]}'

# Vérifier le statut
curl "http://localhost:8000/scans/1"

# Télécharger le rapport
curl "http://localhost:8000/scans/1/report" -o rapport.html
```

### 4. Avec n8n (Optionnel)

```bash
# Installer n8n
npm install -g n8n

# Démarrer n8n
n8n start

# Importer le workflow: n8n_workflow_simple.json
# Accéder à: http://localhost:5678

# Déclencher via webhook
curl -X POST "http://localhost:5678/webhook/websec-scan" \
  -H "Content-Type: application/json" \
  -d '{"target": "example.com", "tools": "nmap,nuclei", "notification_email": "<EMAIL>"}'
```

## 🔧 Outils Intégrés

| Outil | Description | Commande |
|-------|-------------|----------|
| **Nmap** | Scan de ports et services | `nmap -sS -sV --script vuln` |
| **Nuclei** | 3000+ templates de vulnérabilités | `nuclei -u target -severity critical,high` |
| **Nikto** | Scanner web classique | `nikto -h target` |
| **Testssl.sh** | Tests SSL/TLS complets | `testssl.sh target` |

## 🤖 Intelligence Artificielle

### Configuration OpenAI
```python
# Dans websec_scanner.py, ligne 16:
OPENAI_API_KEY = "sk-your-key-here"
```

### Fonctionnalités IA
- ✅ Analyse automatique des vulnérabilités
- ✅ Classification par criticité
- ✅ Recommandations personnalisées
- ✅ Score de risque (1-10)
- ✅ Actions immédiates suggérées

## 📊 Rapports

### Formats disponibles
- **HTML** : Rapport visuel avec graphiques
- **JSON** : Données structurées pour intégration
- **SQLite** : Base de données locale

### Exemple de rapport
```
🛡️ Rapport de Sécurité Web
Target: example.com
Généré le: 15/01/2024 à 14:30:25

📊 Résumé Exécutif
Total des vulnérabilités: 12
• Critiques: 2
• Hautes: 3
• Moyennes: 5
• Basses: 2

🤖 Analyse IA
Score de risque: 7/10
Recommandations prioritaires:
1. Corriger les vulnérabilités SSL
2. Mettre à jour les composants obsolètes
3. Configurer les headers de sécurité
```

## 🔄 Automatisation n8n

### Workflow inclus
- ✅ Déclenchement par webhook
- ✅ Scan automatique
- ✅ Vérification du statut
- ✅ Alertes email si vulnérabilités critiques
- ✅ Génération de rapport

### Cas d'usage
- **Monitoring continu** : Scan quotidien/hebdomadaire
- **CI/CD Integration** : Scan avant déploiement
- **Alertes automatiques** : Email/Slack en cas de problème
- **Rapports programmés** : Envoi automatique aux équipes

## 📈 Exemples Concrets

### Scan d'un site e-commerce
```bash
python3 websec_scanner.py shop.example.com
# Résultat: 15 vulnérabilités trouvées
# - 3 critiques (injection SQL, XSS)
# - 5 hautes (headers manquants)
# - 7 moyennes (versions obsolètes)
```

### Scan SSL d'un site bancaire
```bash
python3 websec_scanner.py https://bank.example.com ssl
# Résultat: Configuration SSL sécurisée
# - TLS 1.3 activé ✅
# - Certificat valide ✅
# - HSTS configuré ✅
```

### Monitoring automatique
```bash
# Crontab pour scan quotidien
0 2 * * * cd /path/to/websec && python3 websec_scanner.py mysite.com
```

## 🛠️ Personnalisation

### Ajouter un nouvel outil
```python
# Dans websec_scanner.py
def scan_custom_tool(target):
    print(f"[CUSTOM] Scan de {target}...")
    command = f"your-tool --target {target}"
    stdout, stderr, returncode, exec_time = run_command(command)
    
    # Parser les résultats...
    vulnerabilities = []
    # Votre logique ici
    
    return {
        'tool': 'custom',
        'target': target,
        'vulnerabilities': vulnerabilities,
        # ...
    }
```

### Modifier l'analyse IA
```python
# Personnaliser le prompt dans analyze_with_ai()
prompt = f"""
Votre prompt personnalisé pour analyser {target}:
- Focus sur les vulnérabilités critiques
- Recommandations spécifiques à votre secteur
- Conformité réglementaire
"""
```

## 🔒 Sécurité et Légalité

### ⚠️ IMPORTANT
- ✅ Utilisez uniquement sur vos propres systèmes
- ✅ Obtenez une autorisation écrite avant tout scan
- ✅ Respectez les conditions d'utilisation des sites
- ❌ N'utilisez jamais sur des systèmes non autorisés

### Bonnes pratiques
- Limitez la bande passante des scans
- Évitez les scans pendant les heures de pointe
- Documentez vos autorisations
- Informez les équipes techniques

## 🆘 Dépannage

### Problèmes courants

**Nuclei ne fonctionne pas**
```bash
# Vérifier l'installation
nuclei -version
# Mettre à jour les templates
nuclei -update-templates
```

**Nmap nécessite des privilèges**
```bash
# Utiliser sudo pour les scans SYN
sudo python3 websec_scanner.py example.com
```

**API ne démarre pas**
```bash
# Vérifier les dépendances
pip3 install -r requirements.txt
# Vérifier le port
netstat -tulpn | grep 8000
```

## 📞 Support

- **Issues** : Créer un ticket GitHub
- **Documentation** : Ce README
- **Exemples** : Dossier `examples/` (à créer)

---

**🎯 Objectif** : Apprendre la cybersécurité avec des outils professionnels réels, du code simple et des résultats concrets !

**🚀 Prêt à scanner ?** Lancez votre premier scan avec :
```bash
python3 websec_scanner.py httpbin.org
```
