#!/usr/bin/env python3
"""
Scanner de Sécurité Web Professionnel
Intègre les meilleurs outils de sécurité pour une analyse complète
"""

import os
import sys
import json
import subprocess
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from urllib.parse import urlparse
import concurrent.futures
import redis
import psycopg2
from psycopg2.extras import RealDictCursor

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/shared/scanner.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ScanResult:
    """Structure pour les résultats de scan"""
    tool: str
    target: str
    timestamp: datetime
    status: str
    vulnerabilities: List[Dict]
    raw_output: str
    execution_time: float
    severity_counts: Dict[str, int]

class DatabaseManager:
    """Gestionnaire de base de données PostgreSQL"""
    
    def __init__(self):
        self.connection_params = {
            'host': os.getenv('POSTGRES_HOST', 'localhost'),
            'database': os.getenv('POSTGRES_DB', 'websec'),
            'user': os.getenv('POSTGRES_USER', 'websec_user'),
            'password': os.getenv('POSTGRES_PASSWORD', 'websec_pass_2024'),
            'port': 5432
        }
    
    def get_connection(self):
        """Obtenir une connexion à la base de données"""
        return psycopg2.connect(**self.connection_params)
    
    def save_scan_result(self, scan_result: ScanResult) -> int:
        """Sauvegarder un résultat de scan"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    query = """
                    INSERT INTO scan_results 
                    (tool, target, timestamp, status, vulnerabilities, raw_output, execution_time, severity_counts)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                    """
                    cursor.execute(query, (
                        scan_result.tool,
                        scan_result.target,
                        scan_result.timestamp,
                        scan_result.status,
                        json.dumps(scan_result.vulnerabilities),
                        scan_result.raw_output,
                        scan_result.execution_time,
                        json.dumps(scan_result.severity_counts)
                    ))
                    return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Erreur sauvegarde DB: {e}")
            return None

class SecurityScanner:
    """Scanner de sécurité principal"""  
    def __init__(self):
        self.db = DatabaseManager()
        self.redis_client = redis.Redis(
            host=os.getenv('REDIS_HOST', 'localhost'),
            port=int(os.getenv('REDIS_PORT', 6379)),
            decode_responses=True
        )
        self.results_dir = "/results"
        os.makedirs(self.results_dir, exist_ok=True)
    
    async def run_command(self, command: List[str], timeout: int = 300) -> tuple:
        """Exécuter une commande avec timeout"""
        start_time = datetime.now()
        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), 
                timeout=timeout
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            return stdout.decode(), stderr.decode(), process.returncode, execution_time
            
        except asyncio.TimeoutError:
            logger.warning(f"Timeout pour commande: {' '.join(command)}")
            return "", "Timeout", -1, timeout
        except Exception as e:
            logger.error(f"Erreur exécution commande: {e}")
            return "", str(e), -1, 0

    async def scan_nmap(self, target: str) -> ScanResult:
        """Scan Nmap professionnel"""
        logger.info(f"Démarrage scan Nmap pour {target}")
        
        command = [
            "nmap", "-sS", "-sV", "-O", "-A", 
            "--script=vuln,safe,discovery",
            "-oX", f"{self.results_dir}/nmap_{target.replace('/', '_')}.xml",
            "-oN", f"{self.results_dir}/nmap_{target.replace('/', '_')}.txt",
            target
        ]
        
        stdout, stderr, returncode, exec_time = await self.run_command(command, 600)
        
        vulnerabilities = self._parse_nmap_output(stdout)
        severity_counts = self._count_severities(vulnerabilities)
        
        return ScanResult(
            tool="nmap",
            target=target,
            timestamp=datetime.now(),
            status="success" if returncode == 0 else "error",
            vulnerabilities=vulnerabilities,
            raw_output=stdout,
            execution_time=exec_time,
            severity_counts=severity_counts
        )

    async def scan_nuclei(self, target: str) -> ScanResult:
        """Scan Nuclei avec templates complets"""
        logger.info(f"Démarrage scan Nuclei pour {target}")
        
        command = [
            "nuclei", "-u", target,
            "-t", "/root/nuclei-templates/",
            "-severity", "low,medium,high,critical",
            "-json",
            "-o", f"{self.results_dir}/nuclei_{target.replace('/', '_')}.json"
        ]
        
        stdout, stderr, returncode, exec_time = await self.run_command(command, 900)
        
        vulnerabilities = self._parse_nuclei_output(stdout)
        severity_counts = self._count_severities(vulnerabilities)
        
        return ScanResult(
            tool="nuclei",
            target=target,
            timestamp=datetime.now(),
            status="success" if returncode == 0 else "error",
            vulnerabilities=vulnerabilities,
            raw_output=stdout,
            execution_time=exec_time,
            severity_counts=severity_counts
        )

    async def scan_nikto(self, target: str) -> ScanResult:
        """Scan Nikto classique"""
        logger.info(f"Démarrage scan Nikto pour {target}")
        
        command = [
            "nikto", "-h", target,
            "-Format", "json",
            "-output", f"{self.results_dir}/nikto_{target.replace('/', '_')}.json"
        ]
        
        stdout, stderr, returncode, exec_time = await self.run_command(command, 600)
        
        vulnerabilities = self._parse_nikto_output(stdout)
        severity_counts = self._count_severities(vulnerabilities)
        
        return ScanResult(
            tool="nikto",
            target=target,
            timestamp=datetime.now(),
            status="success" if returncode == 0 else "error",
            vulnerabilities=vulnerabilities,
            raw_output=stdout,
            execution_time=exec_time,
            severity_counts=severity_counts
        )

    def _parse_nmap_output(self, output: str) -> List[Dict]:
        """Parser la sortie Nmap"""
        vulnerabilities = []
        lines = output.split('\n')
        
        for line in lines:
            if 'VULNERABLE' in line or 'CVE-' in line:
                vuln = {
                    'type': 'nmap_vulnerability',
                    'description': line.strip(),
                    'severity': self._extract_severity_from_line(line),
                    'source': 'nmap'
                }
                vulnerabilities.append(vuln)
        
        return vulnerabilities

    def _parse_nuclei_output(self, output: str) -> List[Dict]:
        """Parser la sortie Nuclei JSON"""
        vulnerabilities = []
        
        for line in output.strip().split('\n'):
            if line.strip():
                try:
                    data = json.loads(line)
                    vuln = {
                        'type': 'nuclei_vulnerability',
                        'template_id': data.get('template-id', ''),
                        'name': data.get('info', {}).get('name', ''),
                        'severity': data.get('info', {}).get('severity', 'info'),
                        'description': data.get('info', {}).get('description', ''),
                        'matched_at': data.get('matched-at', ''),
                        'source': 'nuclei'
                    }
                    vulnerabilities.append(vuln)
                except json.JSONDecodeError:
                    continue
        
        return vulnerabilities

    def _parse_nikto_output(self, output: str) -> List[Dict]:
        """Parser la sortie Nikto"""
        vulnerabilities = []
        
        try:
            if output.strip():
                data = json.loads(output)
                for item in data.get('vulnerabilities', []):
                    vuln = {
                        'type': 'nikto_vulnerability',
                        'id': item.get('id', ''),
                        'method': item.get('method', ''),
                        'uri': item.get('uri', ''),
                        'message': item.get('msg', ''),
                        'severity': 'medium',  # Nikto ne fournit pas toujours la sévérité
                        'source': 'nikto'
                    }
                    vulnerabilities.append(vuln)
        except json.JSONDecodeError:
            pass
        
        return vulnerabilities

    def _extract_severity_from_line(self, line: str) -> str:
        """Extraire la sévérité d'une ligne"""
        line_lower = line.lower()
        if 'critical' in line_lower or 'high' in line_lower:
            return 'high'
        elif 'medium' in line_lower:
            return 'medium'
        elif 'low' in line_lower:
            return 'low'
        else:
            return 'info'

    def _count_severities(self, vulnerabilities: List[Dict]) -> Dict[str, int]:
        """Compter les vulnérabilités par sévérité"""
        counts = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'info': 0}
        
        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'info')
            if severity in counts:
                counts[severity] += 1
            else:
                counts['info'] += 1
        
        return counts

    async def full_scan(self, target: str) -> Dict[str, ScanResult]:
        """Scan complet avec tous les outils"""
        logger.info(f"Démarrage scan complet pour {target}")
        
        # Validation du target
        parsed = urlparse(target if target.startswith('http') else f'http://{target}')
        clean_target = parsed.netloc or parsed.path
        
        # Exécution parallèle des scans
        tasks = [
            self.scan_nmap(clean_target),
            self.scan_nuclei(target),
            self.scan_nikto(target)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        scan_results = {}
        for i, result in enumerate(results):
            tool_name = ['nmap', 'nuclei', 'nikto'][i]
            if isinstance(result, Exception):
                logger.error(f"Erreur scan {tool_name}: {result}")
                continue
            
            scan_results[tool_name] = result
            
            # Sauvegarde en base
            scan_id = self.db.save_scan_result(result)
            if scan_id:
                logger.info(f"Résultat {tool_name} sauvegardé avec ID: {scan_id}")
        
        return scan_results

async def main():
    """Point d'entrée principal"""
    if len(sys.argv) < 2:
        print("Usage: python scanner.py <target>")
        sys.exit(1)
    
    target = sys.argv[1]
    scanner = SecurityScanner()
    
    try:
        results = await scanner.full_scan(target)
        
        # Résumé des résultats
        total_vulns = 0
        for tool, result in results.items():
            vuln_count = len(result.vulnerabilities)
            total_vulns += vuln_count
            logger.info(f"{tool}: {vuln_count} vulnérabilités trouvées")
        
        logger.info(f"Scan terminé. Total: {total_vulns} vulnérabilités")
        
        # Sauvegarde du résumé
        summary = {
            'target': target,
            'timestamp': datetime.now().isoformat(),
            'total_vulnerabilities': total_vulns,
            'tools_used': list(results.keys()),
            'results': {tool: asdict(result) for tool, result in results.items()}
        }
        
        with open(f"{scanner.results_dir}/summary_{target.replace('/', '_')}.json", 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
    except Exception as e:
        logger.error(f"Erreur lors du scan: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
