# Configuration du Scanner de Sécurité Web Professionnel

# Base de données PostgreSQL
POSTGRES_HOST=postgres
POSTGRES_DB=websec
POSTGRES_USER=websec_user
POSTGRES_PASSWORD=websec_pass_2024

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# OpenAI API (pour l'analyse IA)
OPENAI_API_KEY=your_openai_api_key_here

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=websec2024
N8N_HOST=0.0.0.0
N8N_PORT=5678
N8N_PROTOCOL=http

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
JWT_SECRET=your_jwt_secret_here
CORS_ORIGINS=http://localhost:3000,http://localhost:5678

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_N8N_URL=http://localhost:5678

# Grafana
GF_SECURITY_ADMIN_PASSWORD=websec2024

# Alertes (optionnel)
SLACK_WEBHOOK_URL=your_slack_webhook_url
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Scanner Configuration
SCANNER_TIMEOUT=1800
SCANNER_MAX_CONCURRENT=3
SCANNER_RESULTS_RETENTION_DAYS=30

# Sécurité
ENABLE_RATE_LIMITING=true
MAX_REQUESTS_PER_MINUTE=60
ENABLE_API_KEY_AUTH=false

# Monitoring
ENABLE_PROMETHEUS_METRICS=true
LOG_LEVEL=INFO
