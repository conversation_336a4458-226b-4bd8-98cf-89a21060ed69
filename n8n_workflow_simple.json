{"name": "WebSec Scanner Simple", "nodes": [{"parameters": {"httpMethod": "POST", "path": "websec-scan", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "websec-scan-simple"}, {"parameters": {"values": {"string": [{"name": "target", "value": "={{ $json.body.target }}"}, {"name": "tools", "value": "={{ $json.body.tools || 'nmap,nuclei' }}"}, {"name": "notification_email", "value": "={{ $json.body.notification_email || '' }}"}]}}, "id": "extract-params", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"requestMethod": "POST", "url": "http://localhost:8000/scans", "jsonParameters": true, "bodyParametersJson": "={\n  \"target\": \"{{ $json.target }}\",\n  \"tools\": {{ $json.tools.split(',') }}\n}", "options": {}}, "id": "start-scan", "name": "<PERSON>an", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"amount": 30, "unit": "seconds"}, "id": "wait-scan", "name": "Wait for <PERSON><PERSON>", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"requestMethod": "GET", "url": "http://localhost:8000/scans/{{ $json.scan_id }}"}, "id": "check-status", "name": "Check Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.status }}", "operation": "equal", "value2": "completed"}]}}, "id": "check-completed", "name": "Check if Completed", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"requestMethod": "GET", "url": "http://localhost:8000/scans/{{ $json.scan_id }}/results"}, "id": "get-results", "name": "Get Results", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.vulnerability_stats.critical + $json.vulnerability_stats.high }}", "operation": "larger", "value2": 0}]}}, "id": "check-critical", "name": "Check Critical Vulns", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $node['Extract Parameters'].json.notification_email }}", "subject": "🚨 ALERTE SÉCURITÉ - {{ $node['Extract Parameters'].json.target }}", "text": "VULNÉRABILITÉS CRITIQUES DÉTECTÉES!\n\nTarget: {{ $node['Extract Parameters'].json.target }}\n\nRésumé:\n- Critiques: {{ $json.vulnerability_stats.critical }}\n- Hautes: {{ $json.vulnerability_stats.high }}\n- Moyennes: {{ $json.vulnerability_stats.medium }}\n\nConsultez le rapport complet: http://localhost:8000/scans/{{ $json.scan_id }}/report\n\nScan ID: {{ $json.scan_id }}\nDate: {{ $now }}", "options": {}}, "id": "send-alert", "name": "<PERSON> <PERSON><PERSON>", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [2000, 100], "credentials": {"smtp": {"id": "smtp-config", "name": "SMTP Configuration"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"success\",\n  \"message\": \"Scan terminé avec succès\",\n  \"scan_id\": \"{{ $json.scan_id }}\",\n  \"target\": \"{{ $node['Extract Parameters'].json.target }}\",\n  \"vulnerabilities\": {\n    \"critical\": {{ $json.vulnerability_stats.critical }},\n    \"high\": {{ $json.vulnerability_stats.high }},\n    \"medium\": {{ $json.vulnerability_stats.medium }},\n    \"total\": {{ $json.vulnerability_stats.total }}\n  },\n  \"report_url\": \"http://localhost:8000/scans/{{ $json.scan_id }}/report\",\n  \"dashboard_url\": \"http://localhost:8000/dashboard\"\n}"}, "id": "response-success", "name": "Response Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2000, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"completed\",\n  \"message\": \"<PERSON>an termin<PERSON> - <PERSON><PERSON> vulnérabilités critiques\",\n  \"scan_id\": \"{{ $json.scan_id }}\",\n  \"target\": \"{{ $node['Extract Parameters'].json.target }}\",\n  \"vulnerabilities\": {\n    \"critical\": {{ $json.vulnerability_stats.critical }},\n    \"high\": {{ $json.vulnerability_stats.high }},\n    \"medium\": {{ $json.vulnerability_stats.medium }},\n    \"total\": {{ $json.vulnerability_stats.total }}\n  },\n  \"report_url\": \"http://localhost:8000/scans/{{ $json.scan_id }}/report\"\n}"}, "id": "response-normal", "name": "Response Normal", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2000, 300]}, {"parameters": {"amount": 60, "unit": "seconds"}, "id": "wait-more", "name": "Wait More", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1560, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"timeout\",\n  \"message\": \"Le scan prend plus de temps que prévu\",\n  \"scan_id\": \"{{ $node['Start Scan'].json.scan_id }}\",\n  \"target\": \"{{ $node['Extract Parameters'].json.target }}\",\n  \"check_url\": \"http://localhost:8000/scans/{{ $node['Start Scan'].json.scan_id }}\"\n}"}, "id": "response-timeout", "name": "Response Timeout", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 400]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "<PERSON>an", "type": "main", "index": 0}]]}, "Start Scan": {"main": [[{"node": "Wait for <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Wait for Scan": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "Check Status": {"main": [[{"node": "Check if Completed", "type": "main", "index": 0}]]}, "Check if Completed": {"main": [[{"node": "Get Results", "type": "main", "index": 0}], [{"node": "Wait More", "type": "main", "index": 0}]]}, "Get Results": {"main": [[{"node": "Check Critical Vulns", "type": "main", "index": 0}]]}, "Check Critical Vulns": {"main": [[{"node": "<PERSON> <PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Response Success", "type": "main", "index": 0}], [{"node": "Response Normal", "type": "main", "index": 0}]]}, "Wait More": {"main": [[{"node": "Response Timeout", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "Europe/Paris"}, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "websec-scanner-simple", "tags": ["security", "simple", "automation"]}