#!/usr/bin/env python3
"""
Démonstration WebSec Scanner
Script pour tester rapidement le scanner
"""

import os
import time
import requests
import subprocess
import threading

def print_banner():
    print("""
🛡️  WEBSEC SCANNER DEMO
========================

Ce script va démontrer les capacités du scanner sur httpbin.org
(site de test sécurisé et autorisé pour les démonstrations)
""")

def test_scanner_cli():
    print("🔍 Test 1: Scanner en ligne de commande")
    print("-" * 40)
    
    # Test avec httpbin.org (site de test autorisé)
    target = "httpbin.org"
    print(f"Scan de {target} avec Nmap uniquement...")
    
    try:
        result = subprocess.run([
            "python3", "websec_scanner.py", target, "nmap"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ Scan terminé avec succès!")
            print("📄 Vérifiez le fichier de rapport généré dans results/")
        else:
            print(f"❌ Erreur: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("⏰ Timeout - Le scan prend plus de temps que prévu")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_api():
    print("\n🌐 Test 2: API Web")
    print("-" * 40)
    
    # Démarrer l'API en arrière-plan
    print("Démarrage de l'API...")
    api_process = subprocess.Popen([
        "python3", "websec_api.py"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Attendre que l'API démarre
    time.sleep(3)
    
    try:
        # Test de santé
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API démarrée avec succès!")
            print("🌐 Dashboard disponible: http://localhost:8000/dashboard")
            print("📚 Documentation: http://localhost:8000/docs")
            
            # Test de création de scan
            print("\nTest de création de scan...")
            scan_data = {
                "target": "httpbin.org",
                "tools": ["nmap"]
            }
            
            response = requests.post(
                "http://localhost:8000/scans",
                json=scan_data,
                timeout=10
            )
            
            if response.status_code == 200:
                scan_result = response.json()
                print(f"✅ Scan créé avec ID: {scan_result['scan_id']}")
                print("⏳ Le scan s'exécute en arrière-plan...")
            else:
                print(f"❌ Erreur création scan: {response.status_code}")
        else:
            print("❌ API non accessible")
    
    except requests.exceptions.RequestException as e:
        print(f"❌ Erreur connexion API: {e}")
    
    finally:
        # Arrêter l'API
        api_process.terminate()
        print("🛑 API arrêtée")

def test_database():
    print("\n💾 Test 3: Base de données")
    print("-" * 40)
    
    import sqlite3
    
    try:
        conn = sqlite3.connect("websec_results.db")
        cursor = conn.cursor()
        
        # Vérifier les tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if tables:
            print("✅ Base de données initialisée")
            print(f"📊 Tables trouvées: {[table[0] for table in tables]}")
            
            # Compter les scans
            cursor.execute("SELECT COUNT(*) FROM scan_results")
            count = cursor.fetchone()[0]
            print(f"📈 Nombre de résultats de scan: {count}")
        else:
            print("⚠️ Base de données vide - lancez un scan d'abord")
        
        conn.close()
    
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")

def test_tools():
    print("\n🔧 Test 4: Outils de sécurité")
    print("-" * 40)
    
    tools = [
        ("nmap", ["nmap", "--version"]),
        ("nuclei", ["nuclei", "-version"]),
        ("nikto", ["nikto", "-Version"]),
        ("testssl.sh", ["testssl.sh", "--version"])
    ]
    
    for tool_name, command in tools:
        try:
            result = subprocess.run(
                command, 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0:
                version = result.stdout.split('\n')[0]
                print(f"✅ {tool_name}: {version}")
            else:
                print(f"❌ {tool_name}: Non installé ou erreur")
        
        except subprocess.TimeoutExpired:
            print(f"⏰ {tool_name}: Timeout")
        except FileNotFoundError:
            print(f"❌ {tool_name}: Non trouvé")
        except Exception as e:
            print(f"❌ {tool_name}: Erreur - {e}")

def show_examples():
    print("\n📚 Exemples d'utilisation")
    print("-" * 40)
    
    examples = [
        ("Scan complet", "python3 websec_scanner.py example.com"),
        ("Scan rapide", "python3 websec_scanner.py example.com nmap"),
        ("Scan SSL", "python3 websec_scanner.py https://example.com ssl"),
        ("Lister scans", "python3 websec_scanner.py --list"),
        ("API", "python3 websec_api.py"),
        ("Dashboard", "http://localhost:8000/dashboard"),
        ("Webhook n8n", 'curl -X POST "http://localhost:5678/webhook/websec-scan" -d \'{"target":"example.com"}\'')
    ]
    
    for description, command in examples:
        print(f"• {description:15} : {command}")

def main():
    print_banner()
    
    # Vérifier que nous sommes dans le bon répertoire
    if not os.path.exists("websec_scanner.py"):
        print("❌ Erreur: websec_scanner.py non trouvé")
        print("Assurez-vous d'être dans le répertoire du projet")
        return
    
    try:
        # Tests séquentiels
        test_tools()
        test_database()
        
        # Test du scanner (optionnel)
        response = input("\n🔍 Voulez-vous tester le scanner CLI? (y/N): ")
        if response.lower() == 'y':
            test_scanner_cli()
        
        # Test de l'API (optionnel)
        response = input("\n🌐 Voulez-vous tester l'API? (y/N): ")
        if response.lower() == 'y':
            test_api()
        
        show_examples()
        
        print("\n✅ Démonstration terminée!")
        print("🚀 Vous pouvez maintenant utiliser WebSec Scanner")
        print("📖 Consultez README_SIMPLE.md pour plus d'informations")
    
    except KeyboardInterrupt:
        print("\n❌ Démonstration interrompue")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")

main()
