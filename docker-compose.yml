version: '3.8'

services:
  # n8n - Orchestrateur principal
  n8n:
    image: n8nio/n8n:latest
    container_name: websec_n8n
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=websec2024
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - NODE_ENV=production
      - WEBHOOK_URL=http://localhost:5678/
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n/workflows:/home/<USER>/.n8n/workflows
      - ./shared:/shared
    depends_on:
      - postgres
      - redis
    networks:
      - websec_network

  # Base de données PostgreSQL
  postgres:
    image: postgres:15
    container_name: websec_postgres
    environment:
      - POSTGRES_DB=websec
      - POSTGRES_USER=websec_user
      - POSTGRES_PASSWORD=websec_pass_2024
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - websec_network

  # Redis pour cache et queues
  redis:
    image: redis:7-alpine
    container_name: websec_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - websec_network

  # Scanner principal avec tous les outils
  scanner:
    build:
      context: ./scanner
      dockerfile: Dockerfile
    container_name: websec_scanner
    volumes:
      - ./shared:/shared
      - ./scanner/tools:/tools
      - ./scanner/results:/results
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=websec
      - POSTGRES_USER=websec_user
      - POSTGRES_PASSWORD=websec_pass_2024
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - postgres
      - redis
    networks:
      - websec_network

  # API Backend
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: websec_api
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=websec
      - POSTGRES_USER=websec_user
      - POSTGRES_PASSWORD=websec_pass_2024
      - REDIS_HOST=redis
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./shared:/shared
      - ./api/reports:/app/reports
    depends_on:
      - postgres
      - redis
    networks:
      - websec_network

  # Interface Web
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: websec_frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_N8N_URL=http://localhost:5678
    depends_on:
      - api
    networks:
      - websec_network

  # Grafana pour dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: websec_grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=websec2024
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - postgres
    networks:
      - websec_network

volumes:
  n8n_data:
  postgres_data:
  redis_data:
  grafana_data:

networks:
  websec_network:
    driver: bridge
