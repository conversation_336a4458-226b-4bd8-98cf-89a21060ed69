# Interface Web React pour WebSec Scanner
FROM node:18-alpine

# Répertoire de travail
WORKDIR /app

# Copie des fichiers package
COPY package*.json ./

# Installation des dépendances
RUN npm install

# Copie du code source
COPY . .

# Build de l'application
RUN npm run build

# Installation d'un serveur web simple
RUN npm install -g serve

# Exposition du port
EXPOSE 3000

# Commande de démarrage
CMD ["serve", "-s", "build", "-l", "3000"]
