# 🛡️ WebSec Scanner - Analyseur de Sécurité Web Professionnel

Un système complet d'analyse de sécurité web combinant **n8n**, **cybersécurité** et **intelligence artificielle** pour des scans automatisés et des rapports intelligents.

## 🚀 Fonctionnalités

### 🔍 Scan de Sécurité Professionnel
- **Outils intégrés** : Nmap, Nuclei, Nikto, SSLyze, Testssl.sh, Gobuster, SQLmap, XSStrike
- **Types de scan** : Quick (5 min), Full (15 min), Deep (30 min)
- **Scan parallèle** : Exécution simultanée de plusieurs outils
- **Base de données** : Stockage structuré des résultats

### 🤖 Intelligence Artificielle
- **Analyse automatique** des vulnérabilités avec OpenAI GPT
- **Classification intelligente** par criticité
- **Recommandations personnalisées** basées sur le contexte
- **Détection de faux positifs** avec machine learning

### 📊 Rapports et Visualisation
- **Rapports PDF/HTML** professionnels
- **Tableaux de bord** Grafana en temps réel
- **Métriques de sécurité** et tendances
- **Alertes automatiques** Slack/Email

### 🔄 Automatisation n8n
- **Workflows personnalisables** pour différents scénarios
- **Intégration API** complète
- **Monitoring continu** avec re-scan automatique
- **Orchestration** de la réponse aux incidents

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │      n8n        │    │   Scanner       │
│   (React)       │◄──►│  (Orchestrator) │◄──►│   (Kali Tools)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Backend   │    │   PostgreSQL    │    │     Redis       │
│   (FastAPI)     │◄──►│   (Database)    │    │    (Cache)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐
│    Grafana      │
│  (Monitoring)   │
└─────────────────┘
```

## 🛠️ Installation

### Prérequis
- Docker & Docker Compose
- 8GB RAM minimum
- 20GB espace disque
- Clé API OpenAI (optionnel pour l'IA)

### Démarrage rapide

1. **Cloner le projet**
```bash
git clone <repository-url>
cd websec-scanner
```

2. **Configuration**
```bash
cp .env.example .env
# Éditer .env avec vos paramètres
```

3. **Lancement**
```bash
docker-compose up -d
```

4. **Accès aux interfaces**
- **Frontend** : http://localhost:3000
- **API** : http://localhost:8000/docs
- **n8n** : http://localhost:5678 (admin/websec2024)
- **Grafana** : http://localhost:3001 (admin/websec2024)

## 📖 Utilisation

### 1. Lancer un scan via l'interface web
```
1. Ouvrir http://localhost:3000
2. Entrer l'URL cible
3. Choisir le type de scan
4. Cliquer "Démarrer le scan"
```

### 2. Lancer un scan via API
```bash
curl -X POST "http://localhost:8000/scans" \
  -H "Authorization: Bearer demo-token" \
  -H "Content-Type: application/json" \
  -d '{
    "target": "example.com",
    "scan_type": "full"
  }'
```

### 3. Lancer un scan via n8n webhook
```bash
curl -X POST "http://localhost:5678/webhook/scan" \
  -H "Content-Type: application/json" \
  -d '{
    "target": "example.com",
    "scan_type": "quick"
  }'
```

## 🔧 Configuration Avancée

### Outils de scan personnalisés
Modifier `scanner/scanner.py` pour ajouter de nouveaux outils :

```python
async def scan_custom_tool(self, target: str) -> ScanResult:
    command = ["your-tool", "--target", target]
    stdout, stderr, returncode, exec_time = await self.run_command(command)
    # Parser les résultats...
```

### Workflows n8n personnalisés
1. Accéder à n8n : http://localhost:5678
2. Importer `n8n/workflows/security_scan_workflow.json`
3. Personnaliser selon vos besoins

### Intégration IA personnalisée
Modifier `api/main.py` pour personnaliser l'analyse IA :

```python
prompt = f"""
Votre prompt personnalisé pour l'analyse des vulnérabilités...
"""
```

## 📊 Métriques et Monitoring

### Grafana Dashboards
- **Vue d'ensemble** : Statistiques globales
- **Vulnérabilités** : Tendances par sévérité
- **Performance** : Temps d'exécution des scans
- **Alertes** : Incidents de sécurité

### Métriques disponibles
- Nombre de scans par jour/semaine/mois
- Distribution des vulnérabilités par sévérité
- Top 10 des vulnérabilités trouvées
- Temps moyen d'exécution par outil
- Taux de faux positifs

## 🔐 Sécurité

### Authentification
- Authentification basique pour n8n
- JWT tokens pour l'API
- Rate limiting activé
- CORS configuré

### Bonnes pratiques
- Changer les mots de passe par défaut
- Utiliser HTTPS en production
- Restreindre l'accès réseau
- Sauvegarder régulièrement la base de données

## 🧪 Tests

### Tests unitaires
```bash
# API
cd api && python -m pytest tests/

# Scanner
cd scanner && python -m pytest tests/

# Frontend
cd frontend && npm test
```

### Tests d'intégration
```bash
# Test complet du workflow
docker-compose exec api python tests/integration/test_full_workflow.py
```

## 📚 Documentation API

Documentation interactive disponible à : http://localhost:8000/docs

### Endpoints principaux
- `POST /scans` - Créer un nouveau scan
- `GET /scans/{id}` - Obtenir les détails d'un scan
- `GET /vulnerabilities` - Lister les vulnérabilités
- `POST /reports/generate` - Générer un rapport
- `POST /ai/analyze` - Analyse IA des vulnérabilités

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/amazing-feature`)
3. Commit les changements (`git commit -m 'Add amazing feature'`)
4. Push vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrir une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

- **Issues** : Ouvrir un ticket GitHub
- **Documentation** : Wiki du projet
- **Community** : Discord/Slack (liens à venir)

## 🔄 Roadmap

### Version 1.1
- [ ] Support des scans programmés
- [ ] Intégration Shodan API
- [ ] Rapports de conformité (OWASP, NIST)
- [ ] Interface mobile

### Version 1.2
- [ ] Machine learning pour la détection d'anomalies
- [ ] Intégration SIEM
- [ ] API GraphQL
- [ ] Support multi-tenant

---

**⚠️ Avertissement** : Cet outil est destiné uniquement aux tests de sécurité autorisés. L'utilisation sur des systèmes sans autorisation explicite est illégale et contraire à l'éthique.
