{"name": "Security Scan Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "scan", "responseMode": "responseNode", "options": {}}, "id": "webhook-scan-trigger", "name": "Webhook <PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "security-scan-webhook"}, {"parameters": {"values": {"string": [{"name": "target", "value": "={{ $json.body.target }}"}, {"name": "scan_type", "value": "={{ $json.body.scan_type || 'full' }}"}, {"name": "scan_id", "value": "={{ $json.body.scan_id }}"}]}, "options": {}}, "id": "extract-parameters", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.scan_type }}", "operation": "equal", "value2": "quick"}]}}, "id": "check-scan-type", "name": "Check Scan Type", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"requestMethod": "POST", "url": "http://api:8000/scans/{{ $json.scan_id }}/execute", "jsonParameters": true, "bodyParametersJson": "={\n  \"tools\": [\"nuclei\"],\n  \"quick_mode\": true\n}", "options": {"timeout": 600000}}, "id": "quick-scan", "name": "<PERSON>", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 200]}, {"parameters": {"requestMethod": "POST", "url": "http://api:8000/scans/{{ $json.scan_id }}/execute", "jsonParameters": true, "bodyParametersJson": "={\n  \"tools\": [\"nmap\", \"nuclei\", \"nikto\"],\n  \"deep_mode\": false\n}", "options": {"timeout": 1800000}}, "id": "full-scan", "name": "Full Scan", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"requestMethod": "GET", "url": "http://api:8000/scans/{{ $json.scan_id }}/results"}, "id": "get-results", "name": "Get Results", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.critical_vulns + $json.high_vulns }}", "operation": "larger", "value2": 0}]}}, "id": "check-critical-vulns", "name": "Check Critical Vulns", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"requestMethod": "POST", "url": "http://api:8000/ai/analyze", "jsonParameters": true, "bodyParametersJson": "={\n  \"vulnerabilities\": {{ JSON.stringify($json.vulnerabilities) }},\n  \"target_info\": {\n    \"target\": \"{{ $json.target }}\",\n    \"scan_type\": \"{{ $json.scan_type }}\"\n  },\n  \"analysis_type\": \"security_assessment\"\n}"}, "id": "ai-analysis", "name": "AI Analysis", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"requestMethod": "POST", "url": "http://api:8000/reports/generate", "jsonParameters": true, "bodyParametersJson": "={\n  \"scan_id\": \"{{ $json.scan_id }}\",\n  \"report_type\": \"executive\",\n  \"format\": \"pdf\",\n  \"include_ai_analysis\": true\n}"}, "id": "generate-report", "name": "Generate Report", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"channel": "#security-alerts", "text": "🚨 ALERTE SÉCURITÉ CRITIQUE 🚨\n\nTarget: {{ $json.target }}\nVulnérabilités critiques: {{ $json.critical_vulns }}\nVulnérabilités hautes: {{ $json.high_vulns }}\n\nScan ID: {{ $json.scan_id }}\nRapport en cours de génération...", "otherOptions": {"mrkdwn": true}}, "id": "slack-alert", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [1560, 400], "credentials": {"slackApi": {"id": "slack-webhook", "name": "Security <PERSON><PERSON><PERSON>"}}}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "🔴 Vulnérabilités critiques détectées - {{ $json.target }}", "text": "Une analyse de sécurité a révélé des vulnérabilités critiques sur {{ $json.target }}.\n\nRésumé:\n- Vulnérabilités critiques: {{ $json.critical_vulns }}\n- Vulnérabilités hautes: {{ $json.high_vulns }}\n- Vulnérabilités moyennes: {{ $json.medium_vulns }}\n\nScan ID: {{ $json.scan_id }}\nDate: {{ $now }}\n\nVeuillez consulter le rapport détaillé dans l'interface WebSec.", "options": {"allowUnauthorizedCerts": true}}, "id": "email-alert", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [1780, 400], "credentials": {"smtp": {"id": "smtp-security", "name": "Security SMTP"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"success\",\n  \"scan_id\": \"{{ $json.scan_id }}\",\n  \"message\": \"Scan terminé avec succès\",\n  \"vulnerabilities\": {\n    \"critical\": {{ $json.critical_vulns }},\n    \"high\": {{ $json.high_vulns }},\n    \"medium\": {{ $json.medium_vulns }},\n    \"low\": {{ $json.low_vulns }}\n  },\n  \"ai_analysis\": {{ JSON.stringify($json.ai_analysis) }},\n  \"report_id\": \"{{ $json.report_id }}\"\n}"}, "id": "response-success", "name": "Response Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2000, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"completed\",\n  \"scan_id\": \"{{ $json.scan_id }}\",\n  \"message\": \"Scan terminé - Aucune vulnérabilité critique\",\n  \"vulnerabilities\": {\n    \"critical\": {{ $json.critical_vulns }},\n    \"high\": {{ $json.high_vulns }},\n    \"medium\": {{ $json.medium_vulns }},\n    \"low\": {{ $json.low_vulns }}\n  }\n}"}, "id": "response-normal", "name": "Response Normal", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 500]}, {"parameters": {"requestMethod": "POST", "url": "http://api:8000/scans/{{ $json.scan_id }}/schedule-monitoring", "jsonParameters": true, "bodyParametersJson": "={\n  \"monitoring_interval\": \"24h\",\n  \"alert_threshold\": \"high\",\n  \"auto_rescan\": true\n}"}, "id": "schedule-monitoring", "name": "Schedule Monitoring", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 500]}], "connections": {"Webhook Scan Trigger": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "Check Scan Type", "type": "main", "index": 0}]]}, "Check Scan Type": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Full Scan", "type": "main", "index": 0}]]}, "Quick Scan": {"main": [[{"node": "Get Results", "type": "main", "index": 0}]]}, "Full Scan": {"main": [[{"node": "Get Results", "type": "main", "index": 0}]]}, "Get Results": {"main": [[{"node": "Check Critical Vulns", "type": "main", "index": 0}]]}, "Check Critical Vulns": {"main": [[{"node": "AI Analysis", "type": "main", "index": 0}, {"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Schedule Monitoring", "type": "main", "index": 0}]]}, "AI Analysis": {"main": [[{"node": "Generate Report", "type": "main", "index": 0}]]}, "Generate Report": {"main": [[{"node": "Response Success", "type": "main", "index": 0}]]}, "Slack Alert": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Schedule Monitoring": {"main": [[{"node": "Response Normal", "type": "main", "index": 0}]]}}, "active": true, "settings": {"timezone": "Europe/Paris"}, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "security-scan-workflow", "tags": ["security", "automation", "ai"]}