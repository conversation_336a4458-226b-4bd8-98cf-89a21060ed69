#!/usr/bin/env python3
"""
Scanner de Sécurité Web Simple et Efficace
Code direct sans classes, prêt à l'emploi
"""

import os
import sys
import json
import subprocess
import time
import requests
from datetime import datetime
from urllib.parse import urlparse
import sqlite3

# Configuration simple
RESULTS_DIR = "results"
DATABASE_FILE = "websec_results.db"
OPENAI_API_KEY = "your_openai_api_key_here"  # Remplacer par votre clé

# Créer les répertoires nécessaires
os.makedirs(RESULTS_DIR, exist_ok=True)

# Initialiser la base de données SQLite
def init_database():
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS scan_results (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        target TEXT NOT NULL,
        tool TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        vulnerabilities TEXT,
        severity_counts TEXT,
        raw_output TEXT,
        execution_time REAL
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS ai_analysis (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        target TEXT NOT NULL,
        analysis TEXT NOT NULL,
        risk_score INTEGER,
        timestamp TEXT NOT NULL
    )
    ''')
    
    conn.commit()
    conn.close()

# Exécuter une commande système
def run_command(command, timeout=300):
    start_time = time.time()
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        execution_time = time.time() - start_time
        return result.stdout, result.stderr, result.returncode, execution_time
    except subprocess.TimeoutExpired:
        return "", "Timeout", -1, timeout
    except Exception as e:
        return "", str(e), -1, 0

# Scanner Nmap
def scan_nmap(target):
    print(f"[NMAP] Scan de {target}...")
    
    # Nettoyer le target
    clean_target = target.replace("http://", "").replace("https://", "").split("/")[0]
    
    command = f"nmap -sS -sV --script vuln {clean_target}"
    stdout, stderr, returncode, exec_time = run_command(command, 600)
    
    vulnerabilities = []
    severity_counts = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
    
    # Parser les résultats Nmap
    lines = stdout.split('\n')
    for line in lines:
        if 'VULNERABLE' in line or 'CVE-' in line:
            severity = "medium"
            if 'CRITICAL' in line.upper() or 'HIGH' in line.upper():
                severity = "high"
            elif 'LOW' in line.upper():
                severity = "low"
            
            vuln = {
                'type': 'nmap_vulnerability',
                'description': line.strip(),
                'severity': severity,
                'source': 'nmap'
            }
            vulnerabilities.append(vuln)
            severity_counts[severity] += 1
    
    return {
        'tool': 'nmap',
        'target': target,
        'vulnerabilities': vulnerabilities,
        'severity_counts': severity_counts,
        'raw_output': stdout,
        'execution_time': exec_time,
        'status': 'success' if returncode == 0 else 'error'
    }

# Scanner Nuclei
def scan_nuclei(target):
    print(f"[NUCLEI] Scan de {target}...")
    
    command = f"nuclei -u {target} -severity low,medium,high,critical -json"
    stdout, stderr, returncode, exec_time = run_command(command, 900)
    
    vulnerabilities = []
    severity_counts = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
    
    # Parser les résultats Nuclei JSON
    for line in stdout.strip().split('\n'):
        if line.strip():
            try:
                data = json.loads(line)
                severity = data.get('info', {}).get('severity', 'info')
                
                vuln = {
                    'type': 'nuclei_vulnerability',
                    'template_id': data.get('template-id', ''),
                    'name': data.get('info', {}).get('name', ''),
                    'severity': severity,
                    'description': data.get('info', {}).get('description', ''),
                    'matched_at': data.get('matched-at', ''),
                    'source': 'nuclei'
                }
                vulnerabilities.append(vuln)
                severity_counts[severity] += 1
            except json.JSONDecodeError:
                continue
    
    return {
        'tool': 'nuclei',
        'target': target,
        'vulnerabilities': vulnerabilities,
        'severity_counts': severity_counts,
        'raw_output': stdout,
        'execution_time': exec_time,
        'status': 'success' if returncode == 0 else 'error'
    }

# Scanner Nikto
def scan_nikto(target):
    print(f"[NIKTO] Scan de {target}...")
    
    command = f"nikto -h {target} -Format json"
    stdout, stderr, returncode, exec_time = run_command(command, 600)
    
    vulnerabilities = []
    severity_counts = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
    
    # Parser les résultats Nikto
    lines = stdout.split('\n')
    for line in lines:
        if 'OSVDB' in line or 'vulnerability' in line.lower():
            vuln = {
                'type': 'nikto_vulnerability',
                'description': line.strip(),
                'severity': 'medium',  # Nikto ne fournit pas toujours la sévérité
                'source': 'nikto'
            }
            vulnerabilities.append(vuln)
            severity_counts['medium'] += 1
    
    return {
        'tool': 'nikto',
        'target': target,
        'vulnerabilities': vulnerabilities,
        'severity_counts': severity_counts,
        'raw_output': stdout,
        'execution_time': exec_time,
        'status': 'success' if returncode == 0 else 'error'
    }

# Test SSL avec testssl.sh
def scan_ssl(target):
    print(f"[SSL] Test SSL de {target}...")
    
    # Extraire le domaine
    parsed = urlparse(target if target.startswith('http') else f'https://{target}')
    domain = parsed.netloc or parsed.path
    
    command = f"testssl.sh --jsonfile-pretty {RESULTS_DIR}/ssl_{domain.replace('.', '_')}.json {domain}"
    stdout, stderr, returncode, exec_time = run_command(command, 300)
    
    vulnerabilities = []
    severity_counts = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
    
    # Parser les résultats SSL
    lines = stdout.split('\n')
    for line in lines:
        if 'VULNERABLE' in line or 'WEAK' in line or 'INSECURE' in line:
            severity = "high" if 'VULNERABLE' in line else "medium"
            vuln = {
                'type': 'ssl_vulnerability',
                'description': line.strip(),
                'severity': severity,
                'source': 'testssl'
            }
            vulnerabilities.append(vuln)
            severity_counts[severity] += 1
    
    return {
        'tool': 'testssl',
        'target': target,
        'vulnerabilities': vulnerabilities,
        'severity_counts': severity_counts,
        'raw_output': stdout,
        'execution_time': exec_time,
        'status': 'success' if returncode == 0 else 'error'
    }

# Sauvegarder les résultats en base
def save_results(results):
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()
    
    cursor.execute('''
    INSERT INTO scan_results 
    (target, tool, timestamp, vulnerabilities, severity_counts, raw_output, execution_time)
    VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (
        results['target'],
        results['tool'],
        datetime.now().isoformat(),
        json.dumps(results['vulnerabilities']),
        json.dumps(results['severity_counts']),
        results['raw_output'],
        results['execution_time']
    ))
    
    conn.commit()
    conn.close()
    print(f"✅ Résultats {results['tool']} sauvegardés")

# Analyse IA des vulnérabilités
def analyze_with_ai(target, all_vulnerabilities):
    if not OPENAI_API_KEY or OPENAI_API_KEY == "your_openai_api_key_here":
        print("⚠️ Clé OpenAI non configurée, analyse IA ignorée")
        return None
    
    print(f"[IA] Analyse des vulnérabilités pour {target}...")
    
    # Préparer le résumé pour l'IA
    vuln_summary = []
    for vuln in all_vulnerabilities:
        vuln_summary.append({
            'type': vuln.get('type', 'unknown'),
            'severity': vuln.get('severity', 'info'),
            'description': vuln.get('description', '')[:100]
        })
    
    prompt = f"""
    Analysez les vulnérabilités de sécurité suivantes pour {target}:
    
    {json.dumps(vuln_summary, indent=2)}
    
    Fournissez une analyse en JSON avec:
    1. executive_summary: Résumé en 2-3 phrases
    2. main_risks: Liste des 3 principaux risques
    3. recommendations: 3 recommandations prioritaires
    4. risk_score: Score de 1 à 10
    5. immediate_actions: Actions immédiates à prendre
    """
    
    try:
        response = requests.post(
            "https://api.openai.com/v1/chat/completions",
            headers={
                "Authorization": f"Bearer {OPENAI_API_KEY}",
                "Content-Type": "application/json"
            },
            json={
                "model": "gpt-3.5-turbo",
                "messages": [
                    {"role": "system", "content": "Vous êtes un expert en cybersécurité. Répondez uniquement en JSON valide."},
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 1000,
                "temperature": 0.3
            },
            timeout=30
        )
        
        if response.status_code == 200:
            ai_analysis = response.json()['choices'][0]['message']['content']
            
            # Sauvegarder l'analyse IA
            conn = sqlite3.connect(DATABASE_FILE)
            cursor = conn.cursor()
            cursor.execute('''
            INSERT INTO ai_analysis (target, analysis, risk_score, timestamp)
            VALUES (?, ?, ?, ?)
            ''', (target, ai_analysis, 5, datetime.now().isoformat()))  # Score par défaut
            conn.commit()
            conn.close()
            
            print("✅ Analyse IA terminée")
            return ai_analysis
        else:
            print(f"❌ Erreur API OpenAI: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur analyse IA: {e}")
        return None

# Générer un rapport HTML
def generate_html_report(target, scan_results, ai_analysis=None):
    print(f"[RAPPORT] Génération du rapport pour {target}...")

    # Calculer les statistiques
    total_vulns = 0
    severity_totals = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}

    for result in scan_results:
        for severity, count in result['severity_counts'].items():
            severity_totals[severity] += count
            total_vulns += count

    # Template HTML simple
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Rapport de Sécurité - {target}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }}
            .summary {{ background: #ecf0f1; padding: 15px; margin: 20px 0; border-radius: 5px; }}
            .critical {{ color: #e74c3c; font-weight: bold; }}
            .high {{ color: #e67e22; font-weight: bold; }}
            .medium {{ color: #f39c12; font-weight: bold; }}
            .low {{ color: #27ae60; }}
            .tool-section {{ margin: 20px 0; border: 1px solid #bdc3c7; border-radius: 5px; }}
            .tool-header {{ background: #34495e; color: white; padding: 10px; }}
            .vulnerability {{ margin: 10px; padding: 10px; background: #f8f9fa; border-left: 4px solid #3498db; }}
            .ai-section {{ background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 5px; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🛡️ Rapport de Sécurité Web</h1>
            <h2>Target: {target}</h2>
            <p>Généré le: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}</p>
        </div>

        <div class="summary">
            <h3>📊 Résumé Exécutif</h3>
            <p><strong>Total des vulnérabilités:</strong> {total_vulns}</p>
            <ul>
                <li class="critical">Critiques: {severity_totals['critical']}</li>
                <li class="high">Hautes: {severity_totals['high']}</li>
                <li class="medium">Moyennes: {severity_totals['medium']}</li>
                <li class="low">Basses: {severity_totals['low']}</li>
                <li>Informatives: {severity_totals['info']}</li>
            </ul>
        </div>
    """

    # Ajouter l'analyse IA si disponible
    if ai_analysis:
        html_content += f"""
        <div class="ai-section">
            <h3>🤖 Analyse IA</h3>
            <pre>{ai_analysis}</pre>
        </div>
        """

    # Ajouter les résultats par outil
    for result in scan_results:
        html_content += f"""
        <div class="tool-section">
            <div class="tool-header">
                <h3>{result['tool'].upper()} - {len(result['vulnerabilities'])} vulnérabilités trouvées</h3>
                <p>Temps d'exécution: {result['execution_time']:.2f} secondes</p>
            </div>
        """

        for vuln in result['vulnerabilities']:
            severity_class = vuln.get('severity', 'info')
            html_content += f"""
            <div class="vulnerability">
                <h4 class="{severity_class}">🔍 {vuln.get('type', 'Vulnérabilité')} ({vuln.get('severity', 'info').upper()})</h4>
                <p><strong>Description:</strong> {vuln.get('description', 'N/A')}</p>
                <p><strong>Source:</strong> {vuln.get('source', 'N/A')}</p>
            </div>
            """

        html_content += "</div>"

    html_content += """
    </body>
    </html>
    """

    # Sauvegarder le rapport
    report_filename = f"{RESULTS_DIR}/rapport_{target.replace('/', '_').replace(':', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"✅ Rapport généré: {report_filename}")
    return report_filename

# Fonction principale de scan
def scan_website(target, tools=None):
    print(f"\n🚀 Démarrage du scan de sécurité pour: {target}")
    print("=" * 60)

    # Initialiser la base de données
    init_database()

    # Outils par défaut
    if tools is None:
        tools = ['nmap', 'nuclei', 'nikto', 'ssl']

    scan_results = []
    all_vulnerabilities = []

    # Exécuter les scans selon les outils demandés
    if 'nmap' in tools:
        result = scan_nmap(target)
        scan_results.append(result)
        all_vulnerabilities.extend(result['vulnerabilities'])
        save_results(result)

    if 'nuclei' in tools:
        result = scan_nuclei(target)
        scan_results.append(result)
        all_vulnerabilities.extend(result['vulnerabilities'])
        save_results(result)

    if 'nikto' in tools:
        result = scan_nikto(target)
        scan_results.append(result)
        all_vulnerabilities.extend(result['vulnerabilities'])
        save_results(result)

    if 'ssl' in tools:
        result = scan_ssl(target)
        scan_results.append(result)
        all_vulnerabilities.extend(result['vulnerabilities'])
        save_results(result)

    # Analyse IA
    ai_analysis = analyze_with_ai(target, all_vulnerabilities)

    # Générer le rapport
    report_file = generate_html_report(target, scan_results, ai_analysis)

    # Résumé final
    total_vulns = len(all_vulnerabilities)
    print("\n" + "=" * 60)
    print(f"✅ SCAN TERMINÉ pour {target}")
    print(f"📊 Total vulnérabilités trouvées: {total_vulns}")
    print(f"📄 Rapport généré: {report_file}")
    print(f"💾 Résultats sauvegardés dans: {DATABASE_FILE}")
    print("=" * 60)

    return scan_results, report_file

# Fonction pour lister les scans précédents
def list_previous_scans():
    conn = sqlite3.connect(DATABASE_FILE)
    cursor = conn.cursor()

    cursor.execute('''
    SELECT target, tool, timestamp,
           json_extract(severity_counts, '$.critical') as critical,
           json_extract(severity_counts, '$.high') as high,
           json_extract(severity_counts, '$.medium') as medium
    FROM scan_results
    ORDER BY timestamp DESC
    LIMIT 20
    ''')

    results = cursor.fetchall()
    conn.close()

    if results:
        print("\n📋 Derniers scans effectués:")
        print("-" * 80)
        for row in results:
            target, tool, timestamp, critical, high, medium = row
            print(f"{timestamp[:19]} | {tool:8} | {target:30} | C:{critical or 0} H:{high or 0} M:{medium or 0}")
    else:
        print("Aucun scan précédent trouvé.")

# Interface en ligne de commande simple
def main():
    print("🛡️ WebSec Scanner - Scanner de Sécurité Web Simple")
    print("=" * 60)

    # Vérifier les arguments
    if len(sys.argv) < 2:
        print("Usage:")
        print(f"  {sys.argv[0]} <target>                    # Scan complet")
        print(f"  {sys.argv[0]} <target> nmap              # Scan Nmap uniquement")
        print(f"  {sys.argv[0]} <target> nuclei,nikto      # Scan Nuclei et Nikto")
        print(f"  {sys.argv[0]} --list                     # Lister les scans précédents")
        print("\nExemples:")
        print(f"  {sys.argv[0]} example.com")
        print(f"  {sys.argv[0]} https://example.com")
        print(f"  {sys.argv[0]} example.com nmap,ssl")
        return

    # Commande spéciale pour lister
    if sys.argv[1] == '--list':
        list_previous_scans()
        return

    target = sys.argv[1]

    # Outils spécifiés
    tools = None
    if len(sys.argv) > 2:
        tools = sys.argv[2].split(',')

    # Lancer le scan
    try:
        scan_website(target, tools)
    except KeyboardInterrupt:
        print("\n❌ Scan interrompu par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur lors du scan: {e}")

# Point d'entrée
main()
