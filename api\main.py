"""
API Backend pour le Scanner de Sécurité Web
FastAPI avec intégration IA et génération de rapports
"""

import os
import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from uuid import UUID, uuid4

import uvicorn
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON>earer, HTTPAuthorizationCredentials
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel, Field, validator
import psycopg2
from psycopg2.extras import RealDictCursor
import redis
import openai
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
DATABASE_URL = f"postgresql://{os.getenv('POSTGRES_USER', 'websec_user')}:{os.getenv('POSTGRES_PASSWORD', 'websec_pass_2024')}@{os.getenv('POSTGRES_HOST', 'postgres')}/{os.getenv('POSTGRES_DB', 'websec')}"
REDIS_URL = f"redis://{os.getenv('REDIS_HOST', 'redis')}:{os.getenv('REDIS_PORT', 6379)}"
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')

# Configuration OpenAI
if OPENAI_API_KEY:
    openai.api_key = OPENAI_API_KEY

# Base de données
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Redis
redis_client = redis.from_url(REDIS_URL, decode_responses=True)

# FastAPI app
app = FastAPI(
    title="WebSec Scanner API",
    description="API professionnelle pour scanner de sécurité web avec IA",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # À restreindre en production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Models Pydantic
class ScanRequest(BaseModel):
    target: str = Field(..., description="URL ou domaine à scanner")
    scan_type: str = Field(default="full", description="Type de scan: quick, full, deep")
    tools: Optional[List[str]] = Field(default=None, description="Outils spécifiques à utiliser")
    priority: str = Field(default="normal", description="Priorité: low, normal, high")
    
    @validator('target')
    def validate_target(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Target ne peut pas être vide')
        return v.strip()

class ScanResponse(BaseModel):
    scan_id: UUID
    target: str
    status: str
    created_at: datetime
    estimated_duration: int  # en minutes

class VulnerabilityResponse(BaseModel):
    id: UUID
    vuln_type: str
    severity: str
    title: str
    description: str
    affected_url: str
    tool_source: str
    cvss_score: Optional[float]
    cve_id: Optional[str]
    status: str

class ReportRequest(BaseModel):
    scan_id: UUID
    report_type: str = Field(default="standard", description="Type: executive, technical, compliance")
    format: str = Field(default="pdf", description="Format: pdf, html, json")
    include_ai_analysis: bool = Field(default=True, description="Inclure l'analyse IA")

class AIAnalysisRequest(BaseModel):
    vulnerabilities: List[Dict]
    target_info: Dict
    analysis_type: str = Field(default="security_assessment", description="Type d'analyse IA")

# Database helpers
def get_db():
    """Obtenir une session de base de données"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_db_connection():
    """Obtenir une connexion PostgreSQL directe"""
    return psycopg2.connect(
        host=os.getenv('POSTGRES_HOST', 'postgres'),
        database=os.getenv('POSTGRES_DB', 'websec'),
        user=os.getenv('POSTGRES_USER', 'websec_user'),
        password=os.getenv('POSTGRES_PASSWORD', 'websec_pass_2024'),
        port=5432
    )

# Authentication (simplifié pour la démo)
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Authentification basique (à améliorer en production)"""
    # Pour la démo, on accepte tout token non vide
    if not credentials.credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token manquant"
        )
    return {"username": "demo_user", "role": "analyst"}

# Routes principales
@app.get("/")
async def root():
    """Point d'entrée de l'API"""
    return {
        "message": "WebSec Scanner API",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Vérification de santé du système"""
    try:
        # Test base de données
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
        
        # Test Redis
        redis_client.ping()
        
        return {
            "status": "healthy",
            "database": "connected",
            "redis": "connected",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")

@app.post("/scans", response_model=ScanResponse)
async def create_scan(
    scan_request: ScanRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Créer un nouveau scan de sécurité"""
    try:
        scan_id = uuid4()
        
        # Sauvegarde en base
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO scans (id, target, scan_type, status, created_by, metadata)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    scan_id,
                    scan_request.target,
                    scan_request.scan_type,
                    'pending',
                    current_user['username'],
                    json.dumps({
                        'tools': scan_request.tools,
                        'priority': scan_request.priority
                    })
                ))
                conn.commit()
        
        # Lancement du scan en arrière-plan
        background_tasks.add_task(execute_scan, scan_id, scan_request)
        
        # Estimation de durée basée sur le type
        duration_map = {'quick': 5, 'full': 15, 'deep': 30}
        estimated_duration = duration_map.get(scan_request.scan_type, 15)
        
        return ScanResponse(
            scan_id=scan_id,
            target=scan_request.target,
            status='pending',
            created_at=datetime.now(),
            estimated_duration=estimated_duration
        )
        
    except Exception as e:
        logger.error(f"Erreur création scan: {e}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")

@app.get("/scans/{scan_id}")
async def get_scan(scan_id: UUID, current_user: dict = Depends(get_current_user)):
    """Obtenir les détails d'un scan"""
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("""
                    SELECT s.*, 
                           COUNT(sr.id) as results_count,
                           SUM(COALESCE((sr.severity_counts->>'critical')::int, 0)) as critical_vulns,
                           SUM(COALESCE((sr.severity_counts->>'high')::int, 0)) as high_vulns,
                           SUM(COALESCE((sr.severity_counts->>'medium')::int, 0)) as medium_vulns,
                           SUM(COALESCE((sr.severity_counts->>'low')::int, 0)) as low_vulns
                    FROM scans s
                    LEFT JOIN scan_results sr ON s.id = sr.scan_id
                    WHERE s.id = %s
                    GROUP BY s.id
                """, (scan_id,))
                
                scan = cursor.fetchone()
                
                if not scan:
                    raise HTTPException(status_code=404, detail="Scan non trouvé")
                
                return dict(scan)
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur récupération scan: {e}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")

async def execute_scan(scan_id: UUID, scan_request: ScanRequest):
    """Exécuter un scan en arrière-plan"""
    try:
        logger.info(f"Démarrage scan {scan_id} pour {scan_request.target}")
        
        # Mise à jour du statut
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    UPDATE scans SET status = 'running', started_at = NOW()
                    WHERE id = %s
                """, (scan_id,))
                conn.commit()
        
        # Exécution du scanner (appel au container scanner)
        import subprocess
        result = subprocess.run([
            'docker', 'exec', 'websec_scanner',
            'python3', '/app/scanner.py', scan_request.target
        ], capture_output=True, text=True, timeout=1800)
        
        if result.returncode == 0:
            logger.info(f"Scan {scan_id} terminé avec succès")
            status = 'completed'
        else:
            logger.error(f"Scan {scan_id} échoué: {result.stderr}")
            status = 'failed'
        
        # Mise à jour finale
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    UPDATE scans SET status = %s, completed_at = NOW()
                    WHERE id = %s
                """, (status, scan_id))
                conn.commit()
                
    except Exception as e:
        logger.error(f"Erreur exécution scan {scan_id}: {e}")
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    UPDATE scans SET status = 'failed', completed_at = NOW()
                    WHERE id = %s
                """, (scan_id,))
                conn.commit()

# Routes pour l'IA et les rapports
@app.post("/ai/analyze")
async def ai_analysis(
    analysis_request: AIAnalysisRequest,
    current_user: dict = Depends(get_current_user)
):
    """Analyse IA des vulnérabilités"""
    if not OPENAI_API_KEY:
        raise HTTPException(status_code=503, detail="Service IA non configuré")

    try:
        # Préparation du prompt pour l'IA
        vulnerabilities_summary = []
        for vuln in analysis_request.vulnerabilities:
            vulnerabilities_summary.append({
                'type': vuln.get('type', 'unknown'),
                'severity': vuln.get('severity', 'info'),
                'description': vuln.get('description', '')[:200]  # Limiter la taille
            })

        prompt = f"""
        Analysez les vulnérabilités de sécurité suivantes pour le site {analysis_request.target_info.get('target', 'inconnu')}:

        Vulnérabilités trouvées:
        {json.dumps(vulnerabilities_summary, indent=2)}

        Fournissez une analyse structurée incluant:
        1. Résumé exécutif (2-3 phrases)
        2. Risques principaux identifiés
        3. Recommandations prioritaires
        4. Score de risque global (1-10)
        5. Actions immédiates recommandées

        Répondez en JSON avec les clés: executive_summary, main_risks, recommendations, risk_score, immediate_actions
        """

        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "Vous êtes un expert en cybersécurité. Analysez les vulnérabilités et fournissez des recommandations professionnelles."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.3
        )

        ai_analysis = json.loads(response.choices[0].message.content)

        return {
            "analysis": ai_analysis,
            "timestamp": datetime.now().isoformat(),
            "model_used": "gpt-3.5-turbo",
            "vulnerabilities_analyzed": len(analysis_request.vulnerabilities)
        }

    except Exception as e:
        logger.error(f"Erreur analyse IA: {e}")
        raise HTTPException(status_code=500, detail="Erreur lors de l'analyse IA")

@app.post("/reports/generate")
async def generate_report(
    report_request: ReportRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Générer un rapport de sécurité"""
    try:
        # Vérifier que le scan existe
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("SELECT * FROM scans WHERE id = %s", (report_request.scan_id,))
                scan = cursor.fetchone()

                if not scan:
                    raise HTTPException(status_code=404, detail="Scan non trouvé")

        report_id = uuid4()

        # Créer l'entrée rapport
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO reports (id, scan_id, report_type, format, generated_by)
                    VALUES (%s, %s, %s, %s, %s)
                """, (
                    report_id,
                    report_request.scan_id,
                    report_request.report_type,
                    report_request.format,
                    current_user['username']
                ))
                conn.commit()

        # Générer le rapport en arrière-plan
        background_tasks.add_task(
            generate_report_background,
            report_id,
            report_request
        )

        return {
            "report_id": report_id,
            "status": "generating",
            "estimated_time": "2-5 minutes"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur génération rapport: {e}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")

@app.get("/reports/{report_id}")
async def get_report(report_id: UUID, current_user: dict = Depends(get_current_user)):
    """Obtenir un rapport généré"""
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("SELECT * FROM reports WHERE id = %s", (report_id,))
                report = cursor.fetchone()

                if not report:
                    raise HTTPException(status_code=404, detail="Rapport non trouvé")

                return dict(report)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur récupération rapport: {e}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")

@app.get("/reports/{report_id}/download")
async def download_report(report_id: UUID, current_user: dict = Depends(get_current_user)):
    """Télécharger un rapport"""
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("SELECT * FROM reports WHERE id = %s", (report_id,))
                report = cursor.fetchone()

                if not report:
                    raise HTTPException(status_code=404, detail="Rapport non trouvé")

                if not report['file_path'] or not os.path.exists(report['file_path']):
                    raise HTTPException(status_code=404, detail="Fichier rapport non trouvé")

                return FileResponse(
                    path=report['file_path'],
                    filename=f"security_report_{report_id}.{report['format']}",
                    media_type='application/octet-stream'
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erreur téléchargement rapport: {e}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")

@app.get("/scans")
async def list_scans(
    limit: int = 50,
    offset: int = 0,
    status: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """Lister les scans"""
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                where_clause = ""
                params = []

                if status:
                    where_clause = "WHERE status = %s"
                    params.append(status)

                cursor.execute(f"""
                    SELECT * FROM scan_summary
                    {where_clause}
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                """, params + [limit, offset])

                scans = cursor.fetchall()
                return [dict(scan) for scan in scans]

    except Exception as e:
        logger.error(f"Erreur liste scans: {e}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")

@app.get("/vulnerabilities")
async def list_vulnerabilities(
    scan_id: Optional[UUID] = None,
    severity: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    current_user: dict = Depends(get_current_user)
):
    """Lister les vulnérabilités"""
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                where_conditions = []
                params = []

                if scan_id:
                    where_conditions.append("sr.scan_id = %s")
                    params.append(scan_id)

                if severity:
                    where_conditions.append("v.severity = %s")
                    params.append(severity)

                where_clause = ""
                if where_conditions:
                    where_clause = "WHERE " + " AND ".join(where_conditions)

                cursor.execute(f"""
                    SELECT v.*, sr.tool, s.target, s.created_at as scan_date
                    FROM vulnerabilities v
                    JOIN scan_results sr ON v.scan_result_id = sr.id
                    JOIN scans s ON sr.scan_id = s.id
                    {where_clause}
                    ORDER BY v.discovered_at DESC
                    LIMIT %s OFFSET %s
                """, params + [limit, offset])

                vulnerabilities = cursor.fetchall()
                return [dict(vuln) for vuln in vulnerabilities]

    except Exception as e:
        logger.error(f"Erreur liste vulnérabilités: {e}")
        raise HTTPException(status_code=500, detail="Erreur interne du serveur")

async def generate_report_background(report_id: UUID, report_request: ReportRequest):
    """Générer un rapport en arrière-plan"""
    try:
        # Récupérer les données du scan
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Données du scan
                cursor.execute("SELECT * FROM scans WHERE id = %s", (report_request.scan_id,))
                scan = cursor.fetchone()

                # Résultats des outils
                cursor.execute("SELECT * FROM scan_results WHERE scan_id = %s", (report_request.scan_id,))
                results = cursor.fetchall()

                # Vulnérabilités
                cursor.execute("""
                    SELECT v.*, sr.tool
                    FROM vulnerabilities v
                    JOIN scan_results sr ON v.scan_result_id = sr.id
                    WHERE sr.scan_id = %s
                    ORDER BY v.severity DESC, v.discovered_at DESC
                """, (report_request.scan_id,))
                vulnerabilities = cursor.fetchall()

        # Génération du rapport (ici simplifié)
        report_data = {
            'scan': dict(scan),
            'results': [dict(r) for r in results],
            'vulnerabilities': [dict(v) for v in vulnerabilities],
            'generated_at': datetime.now().isoformat()
        }

        # Sauvegarde du rapport
        report_filename = f"report_{report_id}.{report_request.format}"
        report_path = f"/app/reports/{report_filename}"

        if report_request.format == 'json':
            with open(report_path, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
        else:
            # Pour PDF/HTML, utiliser un template (à implémenter)
            with open(report_path, 'w') as f:
                f.write(f"Rapport de sécurité pour {scan['target']}\n")
                f.write(f"Généré le: {datetime.now()}\n")
                f.write(f"Vulnérabilités trouvées: {len(vulnerabilities)}\n")

        # Mise à jour en base
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    UPDATE reports
                    SET file_path = %s, generated_at = NOW()
                    WHERE id = %s
                """, (report_path, report_id))
                conn.commit()

        logger.info(f"Rapport {report_id} généré avec succès")

    except Exception as e:
        logger.error(f"Erreur génération rapport {report_id}: {e}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
