-- Base de données pour le scanner de sécurité web
-- Initialisation des tables et données de base

-- Extension pour UUID
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table des scans
CREATE TABLE IF NOT EXISTS scans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    target VARCHAR(255) NOT NULL,
    scan_type VARCHAR(50) NOT NULL DEFAULT 'full',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_by <PERSON><PERSON><PERSON><PERSON>(100),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Table des résultats de scan
CREATE TABLE IF NOT EXISTS scan_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_id UUID REFERENCES scans(id) ON DELETE CASCADE,
    tool VARCHAR(50) NOT NULL,
    target VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) NOT NULL,
    vulnerabilities JSONB DEFAULT '[]'::jsonb,
    raw_output TEXT,
    execution_time FLOAT DEFAULT 0,
    severity_counts JSONB DEFAULT '{}'::jsonb
);

-- Table des vulnérabilités détaillées
CREATE TABLE IF NOT EXISTS vulnerabilities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_result_id UUID REFERENCES scan_results(id) ON DELETE CASCADE,
    vuln_type VARCHAR(100) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    title VARCHAR(255),
    description TEXT,
    solution TEXT,
    cve_id VARCHAR(20),
    cvss_score FLOAT,
    affected_url VARCHAR(500),
    tool_source VARCHAR(50),
    discovered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'open',
    false_positive BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Table des rapports
CREATE TABLE IF NOT EXISTS reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scan_id UUID REFERENCES scans(id) ON DELETE CASCADE,
    report_type VARCHAR(50) NOT NULL DEFAULT 'standard',
    format VARCHAR(20) NOT NULL DEFAULT 'pdf',
    file_path VARCHAR(500),
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    generated_by VARCHAR(100),
    ai_analysis JSONB DEFAULT '{}'::jsonb,
    executive_summary TEXT,
    recommendations TEXT
);

-- Table des configurations
CREATE TABLE IF NOT EXISTS configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    config_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Table des utilisateurs (simple)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'analyst',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE
);

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_scans_target ON scans(target);
CREATE INDEX IF NOT EXISTS idx_scans_status ON scans(status);
CREATE INDEX IF NOT EXISTS idx_scans_created_at ON scans(created_at);
CREATE INDEX IF NOT EXISTS idx_scan_results_tool ON scan_results(tool);
CREATE INDEX IF NOT EXISTS idx_scan_results_timestamp ON scan_results(timestamp);
CREATE INDEX IF NOT EXISTS idx_vulnerabilities_severity ON vulnerabilities(severity);
CREATE INDEX IF NOT EXISTS idx_vulnerabilities_status ON vulnerabilities(status);
CREATE INDEX IF NOT EXISTS idx_vulnerabilities_cve ON vulnerabilities(cve_id);

-- Index GIN pour les recherches JSON
CREATE INDEX IF NOT EXISTS idx_scan_results_vulnerabilities ON scan_results USING GIN(vulnerabilities);
CREATE INDEX IF NOT EXISTS idx_vulnerabilities_metadata ON vulnerabilities USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_reports_ai_analysis ON reports USING GIN(ai_analysis);

-- Vues utiles
CREATE OR REPLACE VIEW scan_summary AS
SELECT 
    s.id,
    s.target,
    s.status,
    s.created_at,
    s.completed_at,
    COUNT(sr.id) as tools_count,
    SUM(COALESCE((sr.severity_counts->>'critical')::int, 0)) as critical_vulns,
    SUM(COALESCE((sr.severity_counts->>'high')::int, 0)) as high_vulns,
    SUM(COALESCE((sr.severity_counts->>'medium')::int, 0)) as medium_vulns,
    SUM(COALESCE((sr.severity_counts->>'low')::int, 0)) as low_vulns,
    SUM(COALESCE((sr.severity_counts->>'info')::int, 0)) as info_vulns
FROM scans s
LEFT JOIN scan_results sr ON s.id = sr.scan_id
GROUP BY s.id, s.target, s.status, s.created_at, s.completed_at;

-- Vue des vulnérabilités critiques
CREATE OR REPLACE VIEW critical_vulnerabilities AS
SELECT 
    v.*,
    s.target,
    sr.tool,
    s.created_at as scan_date
FROM vulnerabilities v
JOIN scan_results sr ON v.scan_result_id = sr.id
JOIN scans s ON sr.scan_id = s.id
WHERE v.severity IN ('critical', 'high')
AND v.status = 'open'
AND v.false_positive = FALSE
ORDER BY v.discovered_at DESC;

-- Fonctions utiles
CREATE OR REPLACE FUNCTION update_scan_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Mettre à jour le statut du scan parent
    UPDATE scans 
    SET status = CASE 
        WHEN EXISTS(SELECT 1 FROM scan_results WHERE scan_id = NEW.scan_id AND status = 'error') THEN 'failed'
        WHEN NOT EXISTS(SELECT 1 FROM scan_results WHERE scan_id = NEW.scan_id AND status != 'success') THEN 'completed'
        ELSE 'running'
    END,
    completed_at = CASE 
        WHEN NOT EXISTS(SELECT 1 FROM scan_results WHERE scan_id = NEW.scan_id AND status != 'success') THEN NOW()
        ELSE completed_at
    END
    WHERE id = NEW.scan_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mise à jour automatique du statut
CREATE TRIGGER trigger_update_scan_status
    AFTER INSERT OR UPDATE ON scan_results
    FOR EACH ROW
    EXECUTE FUNCTION update_scan_status();

-- Données de configuration par défaut
INSERT INTO configurations (name, description, config_data) VALUES
('default_scan', 'Configuration de scan par défaut', '{
    "tools": ["nmap", "nuclei", "nikto"],
    "nmap_options": ["-sS", "-sV", "-O", "-A", "--script=vuln,safe,discovery"],
    "nuclei_severity": ["low", "medium", "high", "critical"],
    "timeout": 900,
    "concurrent_scans": 3
}'::jsonb),
('quick_scan', 'Scan rapide pour tests', '{
    "tools": ["nuclei"],
    "nuclei_severity": ["high", "critical"],
    "timeout": 300,
    "concurrent_scans": 1
}'::jsonb),
('deep_scan', 'Scan approfondi complet', '{
    "tools": ["nmap", "nuclei", "nikto", "testssl", "gobuster"],
    "nmap_options": ["-sS", "-sV", "-O", "-A", "--script=vuln,safe,discovery,auth,brute"],
    "nuclei_severity": ["info", "low", "medium", "high", "critical"],
    "timeout": 1800,
    "concurrent_scans": 2
}'::jsonb);

-- Utilisateur admin par défaut (mot de passe: websec2024)
INSERT INTO users (username, email, password_hash, role) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e', 'admin');

-- Commentaires sur les tables
COMMENT ON TABLE scans IS 'Table principale des scans de sécurité';
COMMENT ON TABLE scan_results IS 'Résultats détaillés de chaque outil de scan';
COMMENT ON TABLE vulnerabilities IS 'Vulnérabilités détaillées extraites des scans';
COMMENT ON TABLE reports IS 'Rapports générés avec analyse IA';
COMMENT ON TABLE configurations IS 'Configurations de scan prédéfinies';
COMMENT ON TABLE users IS 'Utilisateurs du système';

-- Permissions (à adapter selon vos besoins)
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO websec_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO websec_user;
