# Makefile pour WebSec Scanner
# Commandes pour faciliter le développement et le déploiement

.PHONY: help build start stop restart logs clean test lint setup

# Variables
COMPOSE_FILE = docker-compose.yml
PROJECT_NAME = websec-scanner

# Couleurs pour l'affichage
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

help: ## Afficher l'aide
	@echo "$(BLUE)WebSec Scanner - Commandes disponibles:$(NC)"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}'

setup: ## Configuration initiale du projet
	@echo "$(YELLOW)Configuration initiale...$(NC)"
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "$(GREEN)Fichier .env créé. Veuillez le configurer avant de continuer.$(NC)"; \
	else \
		echo "$(BLUE)Fichier .env existe déjà.$(NC)"; \
	fi
	@mkdir -p shared logs grafana/dashboards grafana/datasources
	@echo "$(GREEN)Configuration terminée!$(NC)"

build: ## Construire tous les containers
	@echo "$(YELLOW)Construction des containers...$(NC)"
	docker-compose -f $(COMPOSE_FILE) build --no-cache
	@echo "$(GREEN)Construction terminée!$(NC)"

start: ## Démarrer tous les services
	@echo "$(YELLOW)Démarrage des services...$(NC)"
	docker-compose -f $(COMPOSE_FILE) up -d
	@echo "$(GREEN)Services démarrés!$(NC)"
	@echo "$(BLUE)Accès aux interfaces:$(NC)"
	@echo "  - Frontend: http://localhost:3000"
	@echo "  - API: http://localhost:8000/docs"
	@echo "  - n8n: http://localhost:5678"
	@echo "  - Grafana: http://localhost:3001"

stop: ## Arrêter tous les services
	@echo "$(YELLOW)Arrêt des services...$(NC)"
	docker-compose -f $(COMPOSE_FILE) down
	@echo "$(GREEN)Services arrêtés!$(NC)"

restart: stop start ## Redémarrer tous les services

logs: ## Afficher les logs de tous les services
	docker-compose -f $(COMPOSE_FILE) logs -f

logs-api: ## Afficher les logs de l'API
	docker-compose -f $(COMPOSE_FILE) logs -f api

logs-scanner: ## Afficher les logs du scanner
	docker-compose -f $(COMPOSE_FILE) logs -f scanner

logs-n8n: ## Afficher les logs de n8n
	docker-compose -f $(COMPOSE_FILE) logs -f n8n

status: ## Afficher le statut des services
	@echo "$(BLUE)Statut des services:$(NC)"
	docker-compose -f $(COMPOSE_FILE) ps

clean: ## Nettoyer les containers et volumes
	@echo "$(YELLOW)Nettoyage...$(NC)"
	docker-compose -f $(COMPOSE_FILE) down -v --remove-orphans
	docker system prune -f
	@echo "$(GREEN)Nettoyage terminé!$(NC)"

clean-all: ## Nettoyage complet (containers, volumes, images)
	@echo "$(RED)Nettoyage complet (ATTENTION: supprime tout)...$(NC)"
	@read -p "Êtes-vous sûr? [y/N] " -n 1 -r; \
	echo; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		docker-compose -f $(COMPOSE_FILE) down -v --remove-orphans; \
		docker system prune -af; \
		docker volume prune -f; \
		echo "$(GREEN)Nettoyage complet terminé!$(NC)"; \
	else \
		echo "$(BLUE)Nettoyage annulé.$(NC)"; \
	fi

test: ## Lancer tous les tests
	@echo "$(YELLOW)Exécution des tests...$(NC)"
	docker-compose -f $(COMPOSE_FILE) exec api python -m pytest tests/ -v
	docker-compose -f $(COMPOSE_FILE) exec scanner python -m pytest tests/ -v
	@echo "$(GREEN)Tests terminés!$(NC)"

test-api: ## Tester uniquement l'API
	docker-compose -f $(COMPOSE_FILE) exec api python -m pytest tests/ -v

test-scanner: ## Tester uniquement le scanner
	docker-compose -f $(COMPOSE_FILE) exec scanner python -m pytest tests/ -v

lint: ## Vérifier le code avec les linters
	@echo "$(YELLOW)Vérification du code...$(NC)"
	docker-compose -f $(COMPOSE_FILE) exec api black --check .
	docker-compose -f $(COMPOSE_FILE) exec api flake8 .
	docker-compose -f $(COMPOSE_FILE) exec scanner black --check .
	docker-compose -f $(COMPOSE_FILE) exec scanner flake8 .
	@echo "$(GREEN)Vérification terminée!$(NC)"

lint-fix: ## Corriger automatiquement le code
	@echo "$(YELLOW)Correction automatique du code...$(NC)"
	docker-compose -f $(COMPOSE_FILE) exec api black .
	docker-compose -f $(COMPOSE_FILE) exec scanner black .
	@echo "$(GREEN)Correction terminée!$(NC)"

backup: ## Sauvegarder la base de données
	@echo "$(YELLOW)Sauvegarde de la base de données...$(NC)"
	@mkdir -p backups
	docker-compose -f $(COMPOSE_FILE) exec postgres pg_dump -U websec_user websec > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)Sauvegarde terminée!$(NC)"

restore: ## Restaurer la base de données (usage: make restore BACKUP=backup_file.sql)
	@if [ -z "$(BACKUP)" ]; then \
		echo "$(RED)Erreur: Spécifiez le fichier de sauvegarde avec BACKUP=filename$(NC)"; \
		exit 1; \
	fi
	@echo "$(YELLOW)Restauration de la base de données...$(NC)"
	docker-compose -f $(COMPOSE_FILE) exec -T postgres psql -U websec_user -d websec < $(BACKUP)
	@echo "$(GREEN)Restauration terminée!$(NC)"

scan: ## Lancer un scan de test (usage: make scan TARGET=example.com)
	@if [ -z "$(TARGET)" ]; then \
		echo "$(RED)Erreur: Spécifiez la cible avec TARGET=domain.com$(NC)"; \
		exit 1; \
	fi
	@echo "$(YELLOW)Lancement du scan pour $(TARGET)...$(NC)"
	curl -X POST "http://localhost:8000/scans" \
		-H "Authorization: Bearer demo-token" \
		-H "Content-Type: application/json" \
		-d '{"target": "$(TARGET)", "scan_type": "quick"}'
	@echo "$(GREEN)Scan lancé!$(NC)"

update: ## Mettre à jour les images Docker
	@echo "$(YELLOW)Mise à jour des images...$(NC)"
	docker-compose -f $(COMPOSE_FILE) pull
	@echo "$(GREEN)Mise à jour terminée!$(NC)"

shell-api: ## Ouvrir un shell dans le container API
	docker-compose -f $(COMPOSE_FILE) exec api bash

shell-scanner: ## Ouvrir un shell dans le container Scanner
	docker-compose -f $(COMPOSE_FILE) exec scanner bash

shell-db: ## Ouvrir un shell dans la base de données
	docker-compose -f $(COMPOSE_FILE) exec postgres psql -U websec_user -d websec

monitor: ## Afficher les métriques en temps réel
	@echo "$(BLUE)Monitoring en temps réel (Ctrl+C pour arrêter):$(NC)"
	watch -n 2 'docker stats --no-stream'

install-tools: ## Installer les outils de développement locaux
	@echo "$(YELLOW)Installation des outils de développement...$(NC)"
	pip install black flake8 pytest pre-commit
	pre-commit install
	@echo "$(GREEN)Outils installés!$(NC)"

dev: ## Mode développement (avec rechargement automatique)
	@echo "$(YELLOW)Démarrage en mode développement...$(NC)"
	docker-compose -f $(COMPOSE_FILE) -f docker-compose.dev.yml up

prod: ## Mode production
	@echo "$(YELLOW)Démarrage en mode production...$(NC)"
	docker-compose -f $(COMPOSE_FILE) -f docker-compose.prod.yml up -d

# Commande par défaut
.DEFAULT_GOAL := help
