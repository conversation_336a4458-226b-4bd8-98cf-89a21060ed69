# Scanner professionnel avec tous les outils de sécurité
FROM kalilinux/kali-rolling:latest

# Mise à jour et installation des dépendances de base
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    curl \
    wget \
    git \
    unzip \
    golang-go \
    nodejs \
    npm \
    nmap \
    nikto \
    sqlmap \
    dirb \
    gobuster \
    whatweb \
    dnsutils \
    netcat-traditional \
    masscan \
    && rm -rf /var/lib/apt/lists/*

# Configuration Go
ENV GOPATH=/root/go
ENV PATH=$PATH:/root/go/bin:/usr/local/go/bin

# Installation des outils Go
RUN go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest && \
    go install -v github.com/projectdiscovery/subfinder/v2/cmd/subfinder@latest && \
    go install -v github.com/projectdiscovery/httpx/cmd/httpx@latest && \
    go install -v github.com/projectdiscovery/katana/cmd/katana@latest && \
    go install -v github.com/tomnomnom/waybackurls@latest && \
    go install -v github.com/tomnomnom/gf@latest

# Installation de Nuclei templates
RUN nuclei -update-templates

# Installation de testssl.sh
RUN git clone --depth 1 https://github.com/drwetter/testssl.sh.git /opt/testssl.sh && \
    chmod +x /opt/testssl.sh/testssl.sh

# Installation de SSLyze
RUN pip3 install sslyze

# Installation de Wapiti
RUN pip3 install wapiti3

# Installation de XSStrike
RUN git clone https://github.com/s0md3v/XSStrike.git /opt/XSStrike && \
    pip3 install -r /opt/XSStrike/requirements.txt

# Installation des dépendances Python pour notre scanner
COPY requirements.txt /tmp/
RUN pip3 install -r /tmp/requirements.txt

# Création des répertoires de travail
RUN mkdir -p /tools /results /shared /app

# Copie du code du scanner
COPY . /app/
WORKDIR /app

# Configuration des permissions
RUN chmod +x /app/scanner.py && \
    chmod +x /opt/testssl.sh/testssl.sh

# Variables d'environnement
ENV PYTHONPATH=/app
ENV PATH=$PATH:/opt/testssl.sh:/root/go/bin

# Point d'entrée
CMD ["python3", "scanner.py"]
