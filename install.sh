#!/bin/bash
# Script d'installation WebSec Scanner Simple
# Compatible Ubuntu/Debian et dérivés

echo "🛡️ Installation WebSec Scanner Simple"
echo "======================================"

# Vérifier les privilèges root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Ce script doit être exécuté en tant que root (sudo)"
    exit 1
fi

# Mise à jour du système
echo "📦 Mise à jour du système..."
apt update && apt upgrade -y

# Installation des dépendances système
echo "🔧 Installation des outils de sécurité..."
apt install -y \
    python3 \
    python3-pip \
    python3-venv \
    nmap \
    nikto \
    curl \
    wget \
    git \
    unzip \
    golang-go \
    nodejs \
    npm

# Configuration Go
export GOPATH=/opt/go
export PATH=$PATH:/opt/go/bin:/usr/local/go/bin
echo 'export GOPATH=/opt/go' >> /etc/environment
echo 'export PATH=$PATH:/opt/go/bin:/usr/local/go/bin' >> /etc/environment

# Création du répertoire Go
mkdir -p /opt/go

# Installation de Nuclei
echo "🔍 Installation de Nuclei..."
go install -v github.com/projectdiscovery/nuclei/v3/cmd/nuclei@latest
cp /opt/go/bin/nuclei /usr/local/bin/

# Mise à jour des templates Nuclei
echo "📋 Mise à jour des templates Nuclei..."
nuclei -update-templates

# Installation de testssl.sh
echo "🔐 Installation de testssl.sh..."
git clone --depth 1 https://github.com/drwetter/testssl.sh.git /opt/testssl.sh
chmod +x /opt/testssl.sh/testssl.sh
ln -sf /opt/testssl.sh/testssl.sh /usr/local/bin/testssl.sh

# Installation des dépendances Python
echo "🐍 Installation des dépendances Python..."
pip3 install -r requirements.txt

# Installation de n8n (optionnel)
read -p "Voulez-vous installer n8n? (y/N): " install_n8n
if [[ $install_n8n =~ ^[Yy]$ ]]; then
    echo "🔄 Installation de n8n..."
    npm install -g n8n
fi

# Création des répertoires
echo "📁 Création des répertoires..."
mkdir -p results
mkdir -p logs
chmod 755 results logs

# Rendre les scripts exécutables
chmod +x websec_scanner.py
chmod +x websec_api.py

# Test des outils
echo "🧪 Test des outils installés..."
echo "Nmap version:"
nmap --version | head -1

echo "Nuclei version:"
nuclei -version

echo "Nikto version:"
nikto -Version | head -1

echo "Testssl.sh version:"
testssl.sh --version | head -1

echo "Python version:"
python3 --version

# Configuration finale
echo "⚙️ Configuration finale..."
cat > websec_config.py << 'EOF'
# Configuration WebSec Scanner
OPENAI_API_KEY = "your_openai_api_key_here"
RESULTS_DIR = "results"
DATABASE_FILE = "websec_results.db"
API_HOST = "0.0.0.0"
API_PORT = 8000
EOF

# Création d'un service systemd pour l'API (optionnel)
read -p "Voulez-vous créer un service systemd pour l'API? (y/N): " create_service
if [[ $create_service =~ ^[Yy]$ ]]; then
    cat > /etc/systemd/system/websec-api.service << EOF
[Unit]
Description=WebSec Scanner API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=$(pwd)
ExecStart=/usr/bin/python3 $(pwd)/websec_api.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable websec-api
    echo "✅ Service websec-api créé. Démarrez-le avec: systemctl start websec-api"
fi

echo ""
echo "✅ Installation terminée!"
echo ""
echo "🚀 Pour commencer:"
echo "1. Configurez votre clé OpenAI dans websec_config.py (optionnel)"
echo "2. Lancez un scan: python3 websec_scanner.py example.com"
echo "3. Démarrez l'API: python3 websec_api.py"
echo "4. Accédez au dashboard: http://localhost:8000/dashboard"
echo ""
echo "📚 Commandes utiles:"
echo "- Scan simple: python3 websec_scanner.py example.com"
echo "- Scan avec outils spécifiques: python3 websec_scanner.py example.com nmap,nuclei"
echo "- Lister les scans: python3 websec_scanner.py --list"
echo "- API documentation: http://localhost:8000/docs"
echo ""
echo "🔧 Outils installés:"
echo "- Nmap: Scan de ports et services"
echo "- Nuclei: Scanner de vulnérabilités moderne"
echo "- Nikto: Scanner web classique"
echo "- Testssl.sh: Tests SSL/TLS"
echo ""
echo "⚠️ IMPORTANT: Utilisez uniquement sur des systèmes autorisés!"
