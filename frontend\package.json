{"name": "websec-scanner-frontend", "version": "1.0.0", "description": "Interface web pour le scanner de sécurité professionnel", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.3.4", "recharts": "^2.5.0", "@mui/material": "^5.11.10", "@mui/icons-material": "^5.11.9", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/x-data-grid": "^5.17.26", "@mui/x-date-pickers": "^5.0.20", "dayjs": "^1.11.7", "react-query": "^3.39.3", "react-hook-form": "^7.43.5", "react-hot-toast": "^2.4.0", "lodash": "^4.17.21", "moment": "^2.29.4", "file-saver": "^2.0.5", "react-syntax-highlighter": "^15.5.0", "react-json-view": "^1.21.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.36.0", "prettier": "^2.8.4"}, "proxy": "http://localhost:8000"}